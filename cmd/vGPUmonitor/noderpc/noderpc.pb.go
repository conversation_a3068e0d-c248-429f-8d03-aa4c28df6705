// Copyright 2015 gRPC authors.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.27.1
// 	protoc        v3.14.0
// source: noderpc/noderpc.proto

package vGPUmonitor

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// The sharedProcs contains the sharedRegion
type ShrregProcSlotT struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Pid    int32    `protobuf:"varint,1,opt,name=pid,proto3" json:"pid,omitempty"`
	Used   []uint64 `protobuf:"varint,2,rep,packed,name=used,proto3" json:"used,omitempty"`
	Status int32    `protobuf:"varint,3,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *ShrregProcSlotT) Reset() {
	*x = ShrregProcSlotT{}
	if protoimpl.UnsafeEnabled {
		mi := &file_noderpc_noderpc_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ShrregProcSlotT) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ShrregProcSlotT) ProtoMessage() {}

func (x *ShrregProcSlotT) ProtoReflect() protoreflect.Message {
	mi := &file_noderpc_noderpc_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ShrregProcSlotT.ProtoReflect.Descriptor instead.
func (*ShrregProcSlotT) Descriptor() ([]byte, []int) {
	return file_noderpc_noderpc_proto_rawDescGZIP(), []int{0}
}

func (x *ShrregProcSlotT) GetPid() int32 {
	if x != nil {
		return x.Pid
	}
	return 0
}

func (x *ShrregProcSlotT) GetUsed() []uint64 {
	if x != nil {
		return x.Used
	}
	return nil
}

func (x *ShrregProcSlotT) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

// The sharedRegionT struct is the main struct for monitoring vgpu
type SharedRegionT struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InitializedFlag int32              `protobuf:"varint,1,opt,name=initializedFlag,proto3" json:"initializedFlag,omitempty"`
	OwnerPid        uint32             `protobuf:"varint,2,opt,name=ownerPid,proto3" json:"ownerPid,omitempty"`
	Sem             uint32             `protobuf:"varint,3,opt,name=sem,proto3" json:"sem,omitempty"`
	Limit           []uint64           `protobuf:"varint,4,rep,packed,name=limit,proto3" json:"limit,omitempty"`
	SmLimit         []uint64           `protobuf:"varint,5,rep,packed,name=sm_limit,json=smLimit,proto3" json:"sm_limit,omitempty"`
	Procs           []*ShrregProcSlotT `protobuf:"bytes,6,rep,name=procs,proto3" json:"procs,omitempty"`
}

func (x *SharedRegionT) Reset() {
	*x = SharedRegionT{}
	if protoimpl.UnsafeEnabled {
		mi := &file_noderpc_noderpc_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SharedRegionT) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SharedRegionT) ProtoMessage() {}

func (x *SharedRegionT) ProtoReflect() protoreflect.Message {
	mi := &file_noderpc_noderpc_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SharedRegionT.ProtoReflect.Descriptor instead.
func (*SharedRegionT) Descriptor() ([]byte, []int) {
	return file_noderpc_noderpc_proto_rawDescGZIP(), []int{1}
}

func (x *SharedRegionT) GetInitializedFlag() int32 {
	if x != nil {
		return x.InitializedFlag
	}
	return 0
}

func (x *SharedRegionT) GetOwnerPid() uint32 {
	if x != nil {
		return x.OwnerPid
	}
	return 0
}

func (x *SharedRegionT) GetSem() uint32 {
	if x != nil {
		return x.Sem
	}
	return 0
}

func (x *SharedRegionT) GetLimit() []uint64 {
	if x != nil {
		return x.Limit
	}
	return nil
}

func (x *SharedRegionT) GetSmLimit() []uint64 {
	if x != nil {
		return x.SmLimit
	}
	return nil
}

func (x *SharedRegionT) GetProcs() []*ShrregProcSlotT {
	if x != nil {
		return x.Procs
	}
	return nil
}

type Podusage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Poduuid     string         `protobuf:"bytes,1,opt,name=poduuid,proto3" json:"poduuid,omitempty"`
	Podvgpuinfo *SharedRegionT `protobuf:"bytes,2,opt,name=podvgpuinfo,proto3" json:"podvgpuinfo,omitempty"`
}

func (x *Podusage) Reset() {
	*x = Podusage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_noderpc_noderpc_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Podusage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Podusage) ProtoMessage() {}

func (x *Podusage) ProtoReflect() protoreflect.Message {
	mi := &file_noderpc_noderpc_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Podusage.ProtoReflect.Descriptor instead.
func (*Podusage) Descriptor() ([]byte, []int) {
	return file_noderpc_noderpc_proto_rawDescGZIP(), []int{2}
}

func (x *Podusage) GetPoduuid() string {
	if x != nil {
		return x.Poduuid
	}
	return ""
}

func (x *Podusage) GetPodvgpuinfo() *SharedRegionT {
	if x != nil {
		return x.Podvgpuinfo
	}
	return nil
}

// The request message containing the user's name.
type GetNodeVGPURequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ctruuid string `protobuf:"bytes,1,opt,name=ctruuid,proto3" json:"ctruuid,omitempty"`
}

func (x *GetNodeVGPURequest) Reset() {
	*x = GetNodeVGPURequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_noderpc_noderpc_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNodeVGPURequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNodeVGPURequest) ProtoMessage() {}

func (x *GetNodeVGPURequest) ProtoReflect() protoreflect.Message {
	mi := &file_noderpc_noderpc_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNodeVGPURequest.ProtoReflect.Descriptor instead.
func (*GetNodeVGPURequest) Descriptor() ([]byte, []int) {
	return file_noderpc_noderpc_proto_rawDescGZIP(), []int{3}
}

func (x *GetNodeVGPURequest) GetCtruuid() string {
	if x != nil {
		return x.Ctruuid
	}
	return ""
}

// The response message containing the greetings
type GetNodeVGPUReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Nodeid       string      `protobuf:"bytes,1,opt,name=nodeid,proto3" json:"nodeid,omitempty"`
	Nodevgpuinfo []*Podusage `protobuf:"bytes,2,rep,name=nodevgpuinfo,proto3" json:"nodevgpuinfo,omitempty"`
}

func (x *GetNodeVGPUReply) Reset() {
	*x = GetNodeVGPUReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_noderpc_noderpc_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNodeVGPUReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNodeVGPUReply) ProtoMessage() {}

func (x *GetNodeVGPUReply) ProtoReflect() protoreflect.Message {
	mi := &file_noderpc_noderpc_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNodeVGPUReply.ProtoReflect.Descriptor instead.
func (*GetNodeVGPUReply) Descriptor() ([]byte, []int) {
	return file_noderpc_noderpc_proto_rawDescGZIP(), []int{4}
}

func (x *GetNodeVGPUReply) GetNodeid() string {
	if x != nil {
		return x.Nodeid
	}
	return ""
}

func (x *GetNodeVGPUReply) GetNodevgpuinfo() []*Podusage {
	if x != nil {
		return x.Nodevgpuinfo
	}
	return nil
}

var File_noderpc_noderpc_proto protoreflect.FileDescriptor

var file_noderpc_noderpc_proto_rawDesc = []byte{
	0x0a, 0x15, 0x6e, 0x6f, 0x64, 0x65, 0x72, 0x70, 0x63, 0x2f, 0x6e, 0x6f, 0x64, 0x65, 0x72, 0x70,
	0x63, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x09, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x72,
	0x70, 0x63, 0x22, 0x4f, 0x0a, 0x0f, 0x73, 0x68, 0x72, 0x72, 0x65, 0x67, 0x50, 0x72, 0x6f, 0x63,
	0x53, 0x6c, 0x6f, 0x74, 0x54, 0x12, 0x10, 0x0a, 0x03, 0x70, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x03, 0x70, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x75, 0x73, 0x65, 0x64, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x04, 0x52, 0x04, 0x75, 0x73, 0x65, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x22, 0xca, 0x01, 0x0a, 0x0d, 0x73, 0x68, 0x61, 0x72, 0x65, 0x64, 0x52, 0x65,
	0x67, 0x69, 0x6f, 0x6e, 0x54, 0x12, 0x28, 0x0a, 0x0f, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x6c,
	0x69, 0x7a, 0x65, 0x64, 0x46, 0x6c, 0x61, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0f,
	0x69, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x6c, 0x69, 0x7a, 0x65, 0x64, 0x46, 0x6c, 0x61, 0x67, 0x12,
	0x1a, 0x0a, 0x08, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x50, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x08, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x50, 0x69, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x73,
	0x65, 0x6d, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x03, 0x73, 0x65, 0x6d, 0x12, 0x14, 0x0a,
	0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x04, 0x20, 0x03, 0x28, 0x04, 0x52, 0x05, 0x6c, 0x69,
	0x6d, 0x69, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x73, 0x6d, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18,
	0x05, 0x20, 0x03, 0x28, 0x04, 0x52, 0x07, 0x73, 0x6d, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x30,
	0x0a, 0x05, 0x70, 0x72, 0x6f, 0x63, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x72, 0x70, 0x63, 0x2e, 0x73, 0x68, 0x72, 0x72, 0x65, 0x67,
	0x50, 0x72, 0x6f, 0x63, 0x53, 0x6c, 0x6f, 0x74, 0x54, 0x52, 0x05, 0x70, 0x72, 0x6f, 0x63, 0x73,
	0x22, 0x60, 0x0a, 0x08, 0x70, 0x6f, 0x64, 0x75, 0x73, 0x61, 0x67, 0x65, 0x12, 0x18, 0x0a, 0x07,
	0x70, 0x6f, 0x64, 0x75, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x70,
	0x6f, 0x64, 0x75, 0x75, 0x69, 0x64, 0x12, 0x3a, 0x0a, 0x0b, 0x70, 0x6f, 0x64, 0x76, 0x67, 0x70,
	0x75, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x70, 0x6c,
	0x75, 0x67, 0x69, 0x6e, 0x72, 0x70, 0x63, 0x2e, 0x73, 0x68, 0x61, 0x72, 0x65, 0x64, 0x52, 0x65,
	0x67, 0x69, 0x6f, 0x6e, 0x54, 0x52, 0x0b, 0x70, 0x6f, 0x64, 0x76, 0x67, 0x70, 0x75, 0x69, 0x6e,
	0x66, 0x6f, 0x22, 0x2e, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x4e, 0x6f, 0x64, 0x65, 0x56, 0x47, 0x50,
	0x55, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x74, 0x72, 0x75,
	0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x74, 0x72, 0x75, 0x75,
	0x69, 0x64, 0x22, 0x63, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x4e, 0x6f, 0x64, 0x65, 0x56, 0x47, 0x50,
	0x55, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x16, 0x0a, 0x06, 0x6e, 0x6f, 0x64, 0x65, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6e, 0x6f, 0x64, 0x65, 0x69, 0x64, 0x12, 0x37,
	0x0a, 0x0c, 0x6e, 0x6f, 0x64, 0x65, 0x76, 0x67, 0x70, 0x75, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x72, 0x70, 0x63,
	0x2e, 0x70, 0x6f, 0x64, 0x75, 0x73, 0x61, 0x67, 0x65, 0x52, 0x0c, 0x6e, 0x6f, 0x64, 0x65, 0x76,
	0x67, 0x70, 0x75, 0x69, 0x6e, 0x66, 0x6f, 0x32, 0x5b, 0x0a, 0x0c, 0x4e, 0x6f, 0x64, 0x65, 0x56,
	0x47, 0x50, 0x55, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x4b, 0x0a, 0x0b, 0x47, 0x65, 0x74, 0x4e, 0x6f,
	0x64, 0x65, 0x56, 0x47, 0x50, 0x55, 0x12, 0x1d, 0x2e, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x72,
	0x70, 0x63, 0x2e, 0x47, 0x65, 0x74, 0x4e, 0x6f, 0x64, 0x65, 0x56, 0x47, 0x50, 0x55, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x72, 0x70,
	0x63, 0x2e, 0x47, 0x65, 0x74, 0x4e, 0x6f, 0x64, 0x65, 0x56, 0x47, 0x50, 0x55, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x22, 0x00, 0x42, 0x4b, 0x0a, 0x1b, 0x69, 0x6f, 0x2e, 0x67, 0x72, 0x70, 0x63, 0x2e,
	0x65, 0x78, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x73, 0x2e, 0x68, 0x65, 0x6c, 0x6c, 0x6f, 0x77, 0x6f,
	0x72, 0x6c, 0x64, 0x42, 0x0f, 0x48, 0x65, 0x6c, 0x6c, 0x6f, 0x57, 0x6f, 0x72, 0x6c, 0x64, 0x50,
	0x72, 0x6f, 0x74, 0x6f, 0x50, 0x01, 0x5a, 0x19, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x34,
	0x70, 0x64, 0x2e, 0x69, 0x6f, 0x2f, 0x76, 0x47, 0x50, 0x55, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f,
	0x72, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_noderpc_noderpc_proto_rawDescOnce sync.Once
	file_noderpc_noderpc_proto_rawDescData = file_noderpc_noderpc_proto_rawDesc
)

func file_noderpc_noderpc_proto_rawDescGZIP() []byte {
	file_noderpc_noderpc_proto_rawDescOnce.Do(func() {
		file_noderpc_noderpc_proto_rawDescData = protoimpl.X.CompressGZIP(file_noderpc_noderpc_proto_rawDescData)
	})
	return file_noderpc_noderpc_proto_rawDescData
}

var file_noderpc_noderpc_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_noderpc_noderpc_proto_goTypes = []interface{}{
	(*ShrregProcSlotT)(nil),    // 0: pluginrpc.shrregProcSlotT
	(*SharedRegionT)(nil),      // 1: pluginrpc.sharedRegionT
	(*Podusage)(nil),           // 2: pluginrpc.podusage
	(*GetNodeVGPURequest)(nil), // 3: pluginrpc.GetNodeVGPURequest
	(*GetNodeVGPUReply)(nil),   // 4: pluginrpc.GetNodeVGPUReply
}
var file_noderpc_noderpc_proto_depIdxs = []int32{
	0, // 0: pluginrpc.sharedRegionT.procs:type_name -> pluginrpc.shrregProcSlotT
	1, // 1: pluginrpc.podusage.podvgpuinfo:type_name -> pluginrpc.sharedRegionT
	2, // 2: pluginrpc.GetNodeVGPUReply.nodevgpuinfo:type_name -> pluginrpc.podusage
	3, // 3: pluginrpc.NodeVGPUInfo.GetNodeVGPU:input_type -> pluginrpc.GetNodeVGPURequest
	4, // 4: pluginrpc.NodeVGPUInfo.GetNodeVGPU:output_type -> pluginrpc.GetNodeVGPUReply
	4, // [4:5] is the sub-list for method output_type
	3, // [3:4] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_noderpc_noderpc_proto_init() }
func file_noderpc_noderpc_proto_init() {
	if File_noderpc_noderpc_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_noderpc_noderpc_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ShrregProcSlotT); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_noderpc_noderpc_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SharedRegionT); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_noderpc_noderpc_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Podusage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_noderpc_noderpc_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetNodeVGPURequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_noderpc_noderpc_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetNodeVGPUReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_noderpc_noderpc_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_noderpc_noderpc_proto_goTypes,
		DependencyIndexes: file_noderpc_noderpc_proto_depIdxs,
		MessageInfos:      file_noderpc_noderpc_proto_msgTypes,
	}.Build()
	File_noderpc_noderpc_proto = out.File
	file_noderpc_noderpc_proto_rawDesc = nil
	file_noderpc_noderpc_proto_goTypes = nil
	file_noderpc_noderpc_proto_depIdxs = nil
}
