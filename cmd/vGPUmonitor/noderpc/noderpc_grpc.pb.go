// Code generated by protoc-gen-go-grpc. DO NOT EDIT.

package vGPUmonitor

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// NodeVGPUInfoClient is the client API for NodeVGPUInfo service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type NodeVGPUInfoClient interface {
	// Sends a greeting
	GetNodeVGPU(ctx context.Context, in *GetNodeVGPURequest, opts ...grpc.CallOption) (*GetNodeVGPUReply, error)
}

type nodeVGPUInfoClient struct {
	cc grpc.ClientConnInterface
}

func NewNodeVGPUInfoClient(cc grpc.ClientConnInterface) NodeVGPUInfoClient {
	return &nodeVGPUInfoClient{cc}
}

func (c *nodeVGPUInfoClient) GetNodeVGPU(ctx context.Context, in *GetNodeVGPURequest, opts ...grpc.CallOption) (*GetNodeVGPUReply, error) {
	out := new(GetNodeVGPUReply)
	err := c.cc.Invoke(ctx, "/pluginrpc.NodeVGPUInfo/GetNodeVGPU", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// NodeVGPUInfoServer is the server API for NodeVGPUInfo service.
// All implementations must embed UnimplementedNodeVGPUInfoServer
// for forward compatibility
type NodeVGPUInfoServer interface {
	// Sends a greeting
	GetNodeVGPU(context.Context, *GetNodeVGPURequest) (*GetNodeVGPUReply, error)
	mustEmbedUnimplementedNodeVGPUInfoServer()
}

// UnimplementedNodeVGPUInfoServer must be embedded to have forward compatible implementations.
type UnimplementedNodeVGPUInfoServer struct {
}

func (UnimplementedNodeVGPUInfoServer) GetNodeVGPU(context.Context, *GetNodeVGPURequest) (*GetNodeVGPUReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNodeVGPU not implemented")
}
func (UnimplementedNodeVGPUInfoServer) mustEmbedUnimplementedNodeVGPUInfoServer() {}

// UnsafeNodeVGPUInfoServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to NodeVGPUInfoServer will
// result in compilation errors.
type UnsafeNodeVGPUInfoServer interface {
	mustEmbedUnimplementedNodeVGPUInfoServer()
}

func RegisterNodeVGPUInfoServer(s grpc.ServiceRegistrar, srv NodeVGPUInfoServer) {
	s.RegisterService(&NodeVGPUInfo_ServiceDesc, srv)
}

func _NodeVGPUInfo_GetNodeVGPU_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetNodeVGPURequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeVGPUInfoServer).GetNodeVGPU(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pluginrpc.NodeVGPUInfo/GetNodeVGPU",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeVGPUInfoServer).GetNodeVGPU(ctx, req.(*GetNodeVGPURequest))
	}
	return interceptor(ctx, in, info, handler)
}

// NodeVGPUInfo_ServiceDesc is the grpc.ServiceDesc for NodeVGPUInfo service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var NodeVGPUInfo_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pluginrpc.NodeVGPUInfo",
	HandlerType: (*NodeVGPUInfoServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetNodeVGPU",
			Handler:    _NodeVGPUInfo_GetNodeVGPU_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "noderpc/noderpc.proto",
}
