/*
Copyright 2025 The HAMi Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package options

import (
	"errors"

	"github.com/go-sql-driver/mysql"
	"github.com/jackc/pgx/v5"
)

// Validate validates server run options, to find
// options' misconfiguration.
func (o *Options) Validate() []error {
	var errorList []error

	if o.MysqlDSN == "" && o.PostgresDSN == "" {
		errorList = append(errorList, errors.New("mysqlDSN and postgresDSN can not be empty at the same time"))
	}
	if o.MysqlDSN != "" {
		_, err := mysql.ParseDSN(o.MysqlDSN)
		if err != nil {
			errorList = append(errorList, err)
		}
	}
	if o.PostgresDSN != "" {
		_, err := pgx.ParseConfig(o.PostgresDSN)
		if err != nil {
			errorList = append(errorList, err)
		}
	}

	errorList = append(errorList, o.ServerRunOptions.Validate()...)
	return errorList
}

func (s *ServerRunOptions) Validate() []error {
	var errList []error
	if s.SecurePort == 0 && s.InsecurePort == 0 {
		errList = append(errList, errors.New("insecure and secure port can not be disabled at the same time"))
	}
	return errList
}
