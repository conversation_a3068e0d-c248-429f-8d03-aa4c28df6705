version: "2"
run:
  concurrency: 4
  modules-download-mode: readonly
output:
  formats:
    text:
      path: stdout
      print-linter-name: true
      print-issued-lines: true
      colors: true 
linters:
  default: none
  enable:
    - asciicheck
    - forcetypeassert
    - godot
    - misspell
    - staticcheck
  settings:
    dupl:
      threshold: 800
    errcheck:
      check-type-assertions: true
      check-blank: true
    errorlint:
      errorf: true
      asserts: true
      comparison: true
    goconst:
      min-len: 3
      min-occurrences: 3
    gocritic:
      disabled-checks:
        - commentedOutCode
        - whyNoLint
      enabled-tags:
        - diagnostic
        - experimental
        - opinionated
        - performance
        - style
      settings:
        hugeParam:
          sizeThreshold: 80
        rangeExprCopy:
          sizeThreshold: 512
        rangeValCopy:
          sizeThreshold: 128
    gocyclo:
      min-complexity: 20
    godot:
      scope: declarations
      capital: false
    nestif:
      min-complexity: 20
  exclusions:
    generated: lax
    presets:
      - comments
      - common-false-positives
      - legacy
      - std-error-handling
    paths:
      - third_party$
      - builtin$
      - examples$
      - pkg/device-plugin 
issues:
  uniq-by-line: true
formatters:
  enable:
    - gofmt
    - goimports
  settings:
    gofmt:
      simplify: true
    gofumpt:
      extra-rules: true
    goimports:
      local-prefixes:
        - github.com/Project-HAMi/HAMi
  exclusions:
    generated: lax
    paths:
      - third_party$
      - builtin$
      - examples$
