apiVersion: v1
kind: Service
metadata:
  name: {{ include "hami-vgpu.scheduler" . }}
  namespace: {{ include "hami-vgpu.namespace" . }}
  labels:
    app.kubernetes.io/component: hami-scheduler
    {{- include "hami-vgpu.labels" . | nindent 4 }}
    {{- if .Values.scheduler.service.labels }}
    {{ toYaml .Values.scheduler.service.labels | indent 4 }}
    {{- end }}
  {{- if .Values.scheduler.service.annotations }}
  annotations: {{ toYaml .Values.scheduler.service.annotations | nindent 4 }}
  {{- end }}
spec:
  type: {{ .Values.scheduler.service.type | default "NodePort" }}  # Default type is NodePort
  ports:
    - name: http
      port: {{ .Values.scheduler.service.httpPort | default 443 }}  # Default HTTP port is 443
      targetPort: {{ .Values.scheduler.service.httpTargetPort | default 443 }}
      {{- if eq (.Values.scheduler.service.type | default "NodePort") "NodePort" }}  # If type is NodePort, set nodePort
      nodePort: {{ .Values.scheduler.service.schedulerPort | default 31998 }}
      {{- end }}
      protocol: TCP
    - name: monitor
      port: {{ .Values.scheduler.service.monitorPort | default 31993 }}  # Default monitoring port is 31993
      targetPort: {{ .Values.scheduler.service.monitorTargetPort | default 9395 }}
      {{- if eq (.Values.scheduler.service.type | default "NodePort") "NodePort" }}  # If type is NodePort, set nodePort
      nodePort: {{ .Values.scheduler.service.monitorPort | default 31993 }}
      {{- end }}
      protocol: TCP
  selector:
    app.kubernetes.io/component: hami-scheduler
    {{- include "hami-vgpu.selectorLabels" . | nindent 4 }}