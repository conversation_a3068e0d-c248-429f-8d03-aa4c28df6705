{{- if .Values.scheduler.admissionWebhook.enabled }}
apiVersion: admissionregistration.k8s.io/v1
kind: MutatingWebhookConfiguration
metadata:
  {{- if .Values.scheduler.certManager.enabled }}
  annotations:
    cert-manager.io/inject-ca-from: {{ include "hami-vgpu.namespace" . }}/{{ include "hami-vgpu.scheduler" . }}-serving-cert
  {{- end }}
  name: {{ include "hami-vgpu.scheduler.webhook" . }}
webhooks:
  - admissionReviewVersions:
    - v1beta1
    clientConfig:
      {{- if .Values.scheduler.admissionWebhook.customURL.enabled }}
      url: https://{{ .Values.scheduler.admissionWebhook.customURL.host}}:{{.Values.scheduler.admissionWebhook.customURL.port}}{{.Values.scheduler.admissionWebhook.customURL.path}}
      {{- else }}
      service:
        name: {{ include "hami-vgpu.scheduler" . }}
        namespace: {{ include "hami-vgpu.namespace" . }}
        path: /webhook
        port: {{ .Values.scheduler.service.httpPort }}
      {{- end }}
    failurePolicy: {{ .Values.scheduler.admissionWebhook.failurePolicy }}
    matchPolicy: Equivalent
    name: vgpu.hami.io
    namespaceSelector:
      matchExpressions:
      - key: hami.io/webhook
        operator: NotIn
        values:
        - ignore
      {{- if .Values.scheduler.admissionWebhook.whitelistNamespaces }}
      - key: kubernetes.io/metadata.name
        operator: NotIn
        values:
        {{- toYaml .Values.scheduler.admissionWebhook.whitelistNamespaces | nindent 10 }}
      {{- end }}
    objectSelector:
      matchExpressions:
      - key: hami.io/webhook
        operator: NotIn
        values:
        - ignore
    reinvocationPolicy: {{ .Values.scheduler.admissionWebhook.reinvocationPolicy }}
    rules:
      - apiGroups:
          - ""
        apiVersions:
          - v1
        operations:
          - CREATE
        resources:
          - pods
        scope: '*'
    sideEffects: None
    timeoutSeconds: 10
{{- end }}