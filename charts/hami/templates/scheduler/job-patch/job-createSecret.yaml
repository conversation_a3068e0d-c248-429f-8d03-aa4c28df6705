{{- if .Values.scheduler.admissionWebhook.enabled }}
{{- if and (.Values.scheduler.patch.enabled) (not .Values.scheduler.certManager.enabled) }}
apiVersion: batch/v1
kind: Job
metadata:
  name: {{ include "hami-vgpu.fullname" . }}-admission-create
  namespace: {{ include "hami-vgpu.namespace" . }}
  annotations:
    "helm.sh/hook": pre-install,pre-upgrade
    "helm.sh/hook-delete-policy": before-hook-creation,hook-succeeded
  labels:
    {{- include "hami-vgpu.labels" . | nindent 4 }}
    app.kubernetes.io/component: admission-webhook
spec:
  {{- if .Capabilities.APIVersions.Has "batch/v1alpha1" }}
  # Alpha feature since k8s 1.12
  ttlSecondsAfterFinished: 0
  {{- end }}
  template:
    metadata:
      name: {{ include "hami-vgpu.fullname" . }}-admission-create
      {{- if .Values.scheduler.patch.podAnnotations }}
      annotations: {{ toYaml .Values.scheduler.patch.podAnnotations | nindent 8 }}
      {{- end }}
      labels:
        {{- include "hami-vgpu.labels" . | nindent 8 }}
        app.kubernetes.io/component: admission-webhook
        hami.io/webhook: ignore
    spec:
      {{- if .Values.scheduler.patch.priorityClassName }}
      priorityClassName: {{ .Values.scheduler.patch.priorityClassName }}
      {{- end }}
      {{- if ge (regexReplaceAll "[^0-9]" .Capabilities.KubeVersion.Minor "" | int) 22 }}
        {{- include "hami.scheduler.patch.new.imagePullSecrets" . | nindent 6 }}
      {{- else }}
        {{- include "hami.scheduler.patch.imagePullSecrets" . | nindent 6 }}
      {{- end }}
      containers:
        - name: create
          {{- if ge (regexReplaceAll "[^0-9]" .Capabilities.KubeVersion.Minor "" | int) 22 }}
          image: {{ include "hami.scheduler.patch.new.image" . }}
          imagePullPolicy: {{ .Values.scheduler.patch.imageNew.pullPolicy }}
          {{- else }}
          image: {{ include "hami.scheduler.patch.image" . }}
          imagePullPolicy: {{ .Values.scheduler.patch.image.pullPolicy }}
          {{- end }}
          args:
            - create
            - --cert-name=tls.crt
            - --key-name=tls.key
            {{- if .Values.scheduler.admissionWebhook.customURL.enabled }}
            - --host={{ printf "%s.%s.svc,127.0.0.1,%s" (include "hami-vgpu.scheduler" .) (include "hami-vgpu.namespace" .) .Values.scheduler.admissionWebhook.customURL.host}}
            {{- else }}
            - --host={{ printf "%s.%s.svc,127.0.0.1" (include "hami-vgpu.scheduler" .) (include "hami-vgpu.namespace" .) }}
            {{- end }}
            - --namespace={{ include "hami-vgpu.namespace" . }}
            - --secret-name={{ include "hami-vgpu.scheduler.tls" . }}
      restartPolicy: OnFailure
      serviceAccountName: {{ include "hami-vgpu.fullname" . }}-admission
      {{- if .Values.scheduler.patch.nodeSelector }}
      nodeSelector: {{ toYaml .Values.scheduler.patch.nodeSelector | nindent 8 }}
      {{- end }}
      {{- if .Values.scheduler.patch.tolerations }}
      tolerations: {{ toYaml .Values.scheduler.patch.tolerations | nindent 8 }}
      {{- end }}
      securityContext:
        runAsNonRoot: true
        runAsUser: {{ .Values.scheduler.patch.runAsUser }}
{{- end }}
{{- end }}
