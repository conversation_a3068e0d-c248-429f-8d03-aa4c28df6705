{{- if .Values.scheduler.admissionWebhook.enabled }}
{{- if and (.Values.scheduler.patch.enabled) (not .Values.scheduler.certManager.enabled) }}
apiVersion: batch/v1
kind: Job
metadata:
  name: {{ include "hami-vgpu.fullname" . }}-admission-patch
  namespace: {{ include "hami-vgpu.namespace" . }}
  annotations:
    "helm.sh/hook": post-install,post-upgrade
    "helm.sh/hook-delete-policy": before-hook-creation,hook-succeeded
  labels:
    {{- include "hami-vgpu.labels" . | nindent 4 }}
    app.kubernetes.io/component: admission-webhook
spec:
  {{- if .Capabilities.APIVersions.Has "batch/v1alpha1" }}
  # Alpha feature since k8s 1.12
  ttlSecondsAfterFinished: 0
  {{- end }}
  template:
    metadata:
      name: {{ include "hami-vgpu.fullname" . }}-admission-patch
      {{- if .Values.scheduler.patch.podAnnotations }}
      annotations: {{ toYaml .Values.scheduler.patch.podAnnotations | nindent 8 }}
      {{- end }}
      labels:
        {{- include "hami-vgpu.labels" . | nindent 8 }}
        app.kubernetes.io/component: admission-webhook
        hami.io/webhook: ignore
    spec:
      {{- if .Values.scheduler.patch.priorityClassName }}
      priorityClassName: {{ .Values.scheduler.patch.priorityClassName }}
      {{- end }}
      {{- if ge (regexReplaceAll "[^0-9]" .Capabilities.KubeVersion.Minor "" | int) 22 }}
        {{- include "hami.scheduler.patch.new.imagePullSecrets" . | nindent 6 }}
      {{- else }}
        {{- include "hami.scheduler.patch.imagePullSecrets" . | nindent 6 }}
      {{- end }}
      containers:
        - name: patch
          {{- if ge (regexReplaceAll "[^0-9]" .Capabilities.KubeVersion.Minor "" | int) 22 }}
          image: {{ include "hami.scheduler.patch.new.image" . }}
          imagePullPolicy: {{ .Values.scheduler.patch.imageNew.pullPolicy }}
          {{- else }}
          image: {{ include "hami.scheduler.patch.image" . }}
          imagePullPolicy: {{ .Values.scheduler.patch.image.pullPolicy }}
          {{- end }}
          args:
            - patch
            - --webhook-name={{ include "hami-vgpu.scheduler.webhook" . }}
            - --namespace={{ include "hami-vgpu.namespace" . }}
            - --patch-validating=false
            - --secret-name={{ include "hami-vgpu.scheduler.tls" . }}
      restartPolicy: OnFailure
      serviceAccountName: {{ include "hami-vgpu.fullname" . }}-admission
      {{- if .Values.scheduler.patch.nodeSelector }}
      nodeSelector: {{ toYaml .Values.scheduler.patch.nodeSelector | nindent 8 }}
      {{- end }}
      {{- if .Values.scheduler.patch.tolerations }}
      tolerations: {{ toYaml .Values.scheduler.patch.tolerations | nindent 8 }}
      {{- end }}
      securityContext:
        runAsNonRoot: true
        runAsUser: {{ .Values.scheduler.patch.runAsUser }}
{{- end }}
{{- end }}
