{{- if .Values.scheduler.kubeScheduler.enabled }}
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "hami-vgpu.scheduler" . }}-newversion
  namespace: {{ include "hami-vgpu.namespace" . }}
  labels:
    app.kubernetes.io/component: hami-scheduler
    {{- include "hami-vgpu.labels" . | nindent 4 }}
data:
  config.yaml: |
    {{- if gt (regexReplaceAll "[^0-9]" .Capabilities.KubeVersion.Minor "" | int) 25}}
    apiVersion: kubescheduler.config.k8s.io/v1
    {{- else }}
    apiVersion: kubescheduler.config.k8s.io/v1beta2
    {{- end }}
    kind: KubeSchedulerConfiguration
    leaderElection:
      leaderElect: false
    profiles:
    - schedulerName: {{ .Values.schedulerName }}
    extenders:
    - urlPrefix: "https://127.0.0.1:443"
      filterVerb: filter
      bindVerb: bind
      nodeCacheCapable: true
      weight: 1
      httpTimeout: 30s
      enableHTTPS: true
      tlsConfig:
        insecure: true
      managedResources:
      - name: {{ .Values.resourceName }}
        ignoredByScheduler: true
      - name: {{ .Values.resourceMem }}
        ignoredByScheduler: true
      - name: {{ .Values.resourceCores }}
        ignoredByScheduler: true
      - name: {{ .Values.resourceMemPercentage }}
        ignoredByScheduler: true
      - name: {{ .Values.resourcePriority }}
        ignoredByScheduler: true
      - name: {{ .Values.mluResourceName }}
        ignoredByScheduler: true
      - name: {{ .Values.dcuResourceName }}
        ignoredByScheduler: true
      - name: {{ .Values.dcuResourceMem }}
        ignoredByScheduler: true
      - name: {{ .Values.dcuResourceCores }}
        ignoredByScheduler: true
      - name: {{ .Values.iluvatarResourceName }}
        ignoredByScheduler: true
      - name: "metax-tech.com/gpu"
        ignoredByScheduler: true
      - name: {{ .Values.metaxResourceName }}
        ignoredByScheduler: true
      - name: {{ .Values.metaxResourceCore }}
        ignoredByScheduler: true
      - name: {{ .Values.metaxResourceMem }}
        ignoredByScheduler: true
      {{- if .Values.devices.ascend.enabled }}
      {{- range .Values.devices.ascend.customresources }}
      - name: {{ . }}
        ignoredByScheduler: true
      {{- end }}
      {{- end }}
      {{- if .Values.devices.mthreads.enabled }}
      {{- range .Values.devices.mthreads.customresources }}
      - name: {{ . }}
        ignoredByScheduler: true
      {{- end }}
      {{- end }}
      {{- if .Values.devices.enflame.enabled }}
      {{- range .Values.devices.enflame.customresources }}
      - name: {{ . }}
        ignoredByScheduler: true
      {{- end }}
      {{- end }}
      {{- if .Values.devices.kunlun.enabled }}
      {{- range .Values.devices.kunlun.customresources }}
      - name: {{ . }}
        ignoredByScheduler: true
      {{- end }}
      {{- end }}
      {{- range .Values.devices.awsneuron.customresources }}
      - name: {{ . }}
        ignoredByScheduler: true
      {{- end }}
{{- end }}
