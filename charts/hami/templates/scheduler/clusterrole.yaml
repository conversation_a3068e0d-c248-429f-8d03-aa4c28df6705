apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: {{ include "hami-vgpu.scheduler" . }}
  labels:
    app.kubernetes.io/component: "hami-scheduler"
    {{- include "hami-vgpu.labels" . | nindent 4 }}
rules:
  - apiGroups: [""]
    resources: ["pods", "configmaps"]
    verbs: ["get", "list", "watch", "patch"]
  - apiGroups: [""]
    resources: ["pods/binding"]
    verbs: ["create"]
  - apiGroups: [""]
    resources: ["nodes"]
    verbs: ["get", "list", "patch", "watch"]
  - apiGroups: [""]
    resources: ["events"]
    verbs: ["create", "get", "list"]
  - apiGroups: [""]
    resources: ["resourcequotas"]
    verbs: ["get", "list", "watch"]
  - apiGroups: [""]
    resources: ["secrets"]
    verbs: ["get","list","watch"]

