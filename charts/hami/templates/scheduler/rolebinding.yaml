apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: {{ include "hami-vgpu.scheduler" . }}
  namespace: {{ include "hami-vgpu.namespace" . }}
  labels:
    app.kubernetes.io/component: "hami-scheduler"
    {{- include "hami-vgpu.labels" . | nindent 4 }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: {{ include "hami-vgpu.scheduler" . }}
subjects:
  - kind: ServiceAccount
    name: {{ include "hami-vgpu.scheduler" . }}
    namespace: {{ include "hami-vgpu.namespace" . }}
