## @section Global configuration
## @param global.imageRegistry Global Docker image registry
## @param global.imagePullSecrets Global Docker image pull secrets
global:
  ## @param global.imageRegistry Global Docker image registry
  imageRegistry: ""
  ## E.g.
  ## imagePullSecrets:
  ##   - myRegistryKeySecretName
  ## @param global.imagePullSecrets Global Docker image pull secrets
  imagePullSecrets: []
  imageTag: "v2.6.0"
  gpuHookPath: /usr/local
  labels: {}
  annotations: {}
  managedNodeSelectorEnable: false
  managedNodeSelector:
    usage: "gpu"

nameOverride: ""
fullnameOverride: ""
namespaceOverride: ""

#Nvidia GPU Parameters
resourceName: "nvidia.com/gpu"
resourceMem: "nvidia.com/gpumem"
resourceMemPercentage: "nvidia.com/gpumem-percentage"
resourceCores: "nvidia.com/gpucores"
resourcePriority: "nvidia.com/priority"

#MLU Parameters
mluResourceName: "cambricon.com/vmlu"
mluResourceMem: "cambricon.com/mlu.smlu.vmemory"
mluResourceCores: "cambricon.com/mlu.smlu.vcore"

#Hygon DCU Parameters
dcuResourceName: "hygon.com/dcunum"
dcuResourceMem: "hygon.com/dcumem"
dcuResourceCores: "hygon.com/dcucores"

#Iluvatar GPU Parameters
iluvatarResourceName: "iluvatar.ai/vgpu"
iluvatarResourceMem: "iluvatar.ai/vcuda-memory"
iluvatarResourceCore: "iluvatar.ai/vcuda-core"

#Metax sGPU Parameters
metaxResourceName: "metax-tech.com/sgpu"
metaxResourceCore: "metax-tech.com/vcore"
metaxResourceMem: "metax-tech.com/vmemory"
metaxsGPUTopologyAware: "false"

#Kunlun XPU Parameters
kunlunResourceName: "kunlunxin.com/xpu"

schedulerName: "hami-scheduler"

podSecurityPolicy:
  enabled: false

scheduler:
  # @param nodeName defines the node name and the nvidia-vgpu-scheduler-scheduler will schedule to the node.
  # if we install the nvidia-vgpu-scheduler-scheduler as default scheduler, we need to remove the k8s default
  # scheduler pod from the cluster first, we must specify node name to skip the schedule workflow.
  nodeName: ""
  #nodeLabelSelector:
  #  "gpu": "on"
  overwriteEnv: "false"
  defaultSchedulerPolicy:
    nodeSchedulerPolicy: binpack
    gpuSchedulerPolicy: spread
  metricsBindAddress: ":9395"
  # If set to false, When Pod.Spec.SchedulerName equals to the const DefaultSchedulerName in k8s.io/api/core/v1 package, webhook will not overwrite it
  forceOverwriteDefaultScheduler: true
  livenessProbe: false
  leaderElect: true
  # when leaderElect is true, replicas is available, otherwise replicas is 1.
  replicas: 1
  kubeScheduler:
    # @param enabled indicate whether to run kube-scheduler container in the scheduler pod, it's true by default.
    enabled: true
    ## @param image.registry kube scheduler image registry
    ## @param image.repository kube scheduler image repository
    ## @param image.tag kube scheduler image tag (immutable tags are recommended)
    ## @param image.pullPolicy kube scheduler image pull policy
    ## @param image.pullSecrets Specify docker-registry secret names as an array
    image:
      registry: "registry.cn-hangzhou.aliyuncs.com"
      repository: "google_containers/kube-scheduler"
      tag: ""
      ## Specify a imagePullPolicy
      ## Defaults to 'Always' if image tag is 'latest', else set to 'IfNotPresent'
      ## ref: https://kubernetes.io/docs/user-guide/images/#pre-pulling-images
      ##
      pullPolicy: IfNotPresent
      ## Optionally specify an array of imagePullSecrets.
      ## Secrets must be manually created in the namespace.
      ## Example:
      ## pullSecrets:
      ##   - myRegistryKeySecretName
      ##
      pullSecrets: []
    resources: {}
      # If you do want to specify resources, uncomment the following lines, adjust them as necessary.
      # and remove the curly braces after 'resources:'.
#      limits:
#        cpu: 1000m
#        memory: 1000Mi
#      requests:
#        cpu: 100m
#        memory: 100Mi
    extraNewArgs:
      - --config=/config/config.yaml
      - -v=4
    extraArgs:
      - --policy-config-file=/config/config.json
      - -v=4
  extender:
    ## @param image.registry scheduler extender image registry
    ## @param image.repository scheduler extender image repository
    ## @param image.tag scheduler extender image tag (immutable tags are recommended)
    ## @param image.pullPolicy scheduler extender image pull policy
    ## @param image.pullSecrets Specify docker-registry secret names as an array
    image:
      registry: "docker.io"
      repository: "projecthami/hami"
      tag: ""
      ## Specify a imagePullPolicy
      ## Defaults to 'Always' if image tag is 'latest', else set to 'IfNotPresent'
      ## ref: https://kubernetes.io/docs/user-guide/images/#pre-pulling-images
      ##
      pullPolicy: IfNotPresent
      ## Optionally specify an array of imagePullSecrets.
      ## Secrets must be manually created in the namespace.
      ## Example:
      ## pullSecrets:
      ##   - myRegistryKeySecretName
      ##
      pullSecrets: []
    resources: {}
      # If you do want to specify resources, uncomment the following lines, adjust them as necessary,
      # and remove the curly braces after 'resources:'.
#      limits:
#        cpu: 1000m
#        memory: 1000Mi
#      requests:
#        cpu: 100m
#        memory: 100Mi
    extraArgs:
      - --debug
      - -v=4
  nodeLockExpire: "5m"
  podAnnotations: {}
  tolerations: []
  #serviceAccountName: "hami-vgpu-scheduler-sa"
  admissionWebhook:
    # If set to false, the admission webhook is not installed and any pods that should use HAMi must be
    # configured to use the right 'schedulerName' and other device-specific configurations.
    enabled: true
    customURL:
      enabled: false
      # must be an endpoint using https.
      # should generate host certs here
      host: 127.0.0.1 # hostname or ip, can be your node'IP if you want to use https://<nodeIP>:<schedulerPort>/<path>
      port: 31998
      path: /webhook
    whitelistNamespaces:
    # Specify the namespaces that the webhook will not be applied to.
      # - default
      # - kube-system
      # - istio-system
    reinvocationPolicy: Never
    failurePolicy: Ignore
  ## TLS Certificate Option 1: Use cert-manager to generate self-signed certificate.
  ## If enabled, always takes precedence over options 2.
  certManager:
    enabled: false
  ## TLS Certificate Option 2: Use kube-webhook-certgen to generate self-signed certificate.
  ## If true and certManager.enabled is false, Helm will automatically create a self-signed cert and secret for you.
  patch:
    enabled: true
    ## @param image.registry scheduler extender image registry
    ## @param image.repository scheduler extender image repository
    ## @param image.tag scheduler extender image tag (immutable tags are recommended)
    ## @param image.pullPolicy scheduler extender image pull policy
    ## @param image.pullSecrets Specify docker-registry secret names as an array
    image:
      registry: "docker.io"
      repository: "jettech/kube-webhook-certgen"
      tag: "v1.5.2"
      ## Specify a imagePullPolicy
      ## Defaults to 'Always' if image tag is 'latest', else set to 'IfNotPresent'
      ## ref: https://kubernetes.io/docs/user-guide/images/#pre-pulling-images
      ##
      pullPolicy: IfNotPresent
      ## Optionally specify an array of imagePullSecrets.
      ## Secrets must be manually created in the namespace.
      ## Example:
      ## pullSecrets:
      ##   - myRegistryKeySecretName
      ##
      pullSecrets: []
    ## @param image.registry scheduler extender image registry
    ## @param image.repository scheduler extender image repository
    ## @param image.tag scheduler extender image tag (immutable tags are recommended)
    ## @param image.pullPolicy scheduler extender image pull policy
    ## @param image.pullSecrets Specify docker-registry secret names as an array
    imageNew: 
      registry: "docker.io"
      repository: "liangjw/kube-webhook-certgen"
      tag: "v1.1.1"
      ## Specify a imagePullPolicy
      ## Defaults to 'Always' if image tag is 'latest', else set to 'IfNotPresent'
      ## ref: https://kubernetes.io/docs/user-guide/images/#pre-pulling-images
      ##
      pullPolicy: IfNotPresent
      ## Optionally specify an array of imagePullSecrets.
      ## Secrets must be manually created in the namespace.
      ## Example:
      ## pullSecrets:
      ##   - myRegistryKeySecretName
      ##
      pullSecrets: []
    priorityClassName: ""
    podAnnotations: {}
    nodeSelector: {}
    tolerations: []
    runAsUser: 2000
  service:
    type: NodePort  # Default type is NodePort, can be changed to ClusterIP
    httpPort: 443   # HTTP port
    schedulerPort: 31998  # NodePort for HTTP
    monitorPort: 31993    # Monitoring port
    monitorTargetPort: 9395
    labels: {}
    annotations: {}

devicePlugin:
  ## @param image.registry devicePlugin image registry
  ## @param image.repository devicePlugin image repository
  ## @param image.tag devicePlugin image tag (immutable tags are recommended)
  ## @param image.pullPolicy devicePlugin image pull policy
  ## @param image.pullSecrets Specify docker-registry secret names as an array
  image:
    registry: "docker.io"
    repository: "projecthami/hami"
    tag: ""
    ## Specify a imagePullPolicy
    ## Defaults to 'Always' if image tag is 'latest', else set to 'IfNotPresent'
    ## ref: https://kubernetes.io/docs/user-guide/images/#pre-pulling-images
    ##
    pullPolicy: IfNotPresent
    ## Optionally specify an array of imagePullSecrets.
    ## Secrets must be manually created in the namespace.
    ## Example:
    ## pullSecrets:
    ##   - myRegistryKeySecretName
    ##
    pullSecrets: []
  monitor:
    ## @param image.registry monitor image registry
    ## @param image.repository monitor image repository
    ## @param image.tag monitor image tag (immutable tags are recommended)
    ## @param image.pullPolicy monitor image pull policy
    ## @param image.pullSecrets Specify docker-registry secret names as an array
    image:
      registry: "docker.io"
      repository: "projecthami/hami"
      tag: ""
      ## Specify a imagePullPolicy
      ## Defaults to 'Always' if image tag is 'latest', else set to 'IfNotPresent'
      ## ref: https://kubernetes.io/docs/user-guide/images/#pre-pulling-images
      ##
      pullPolicy: IfNotPresent
      ## Optionally specify an array of imagePullSecrets.
      ## Secrets must be manually created in the namespace.
      ## Example:
      ## pullSecrets:
      ##   - myRegistryKeySecretName
      ##
      pullSecrets: []
    ctrPath: /usr/local/vgpu/containers
  deviceSplitCount: 10
  deviceMemoryScaling: 1
  deviceCoreScaling: 1
  # The runtime class name to be used by the device plugin, and added to the pod.spec.runtimeClassName of applications utilizing NVIDIA GPUs
  runtimeClassName: ""
  # Whether to create runtime class, name comes from runtimeClassName when it is set
  createRuntimeClass: false
  migStrategy: "none"
  disablecorelimit: "false"
  passDeviceSpecsEnabled: false
  extraArgs:
    - -v=4

  service:
    type: NodePort  # Default type is NodePort, can be changed to ClusterIP
    httpPort: 31992
    labels: {}
    annotations: {}

  pluginPath: /var/lib/kubelet/device-plugins
  libPath: /usr/local/vgpu

  podAnnotations: {}
  nvidianodeSelector:
    gpu: "on"
  tolerations: []
  # The updateStrategy for DevicePlugin DaemonSet.
  # If you want to update the DaemonSet by manual, set type as "OnDelete".
  # We recommend use OnDelete update strategy because DevicePlugin pod restart will cause business pod restart, this behavior is destructive.
  # Otherwise, you can use RollingUpdate update strategy to rolling update DevicePlugin pod.
  updateStrategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1

  resources: {}
    # If you do want to specify resources, uncomment the following lines, adjust them as necessary.
    # and remove the curly braces after 'resources:'.
#    limits:
#       cpu: 1000m
#       memory: 1000Mi
#    requests:
#      cpu: 100m
#      memory: 100Mi

  vgpuMonitor:
    resources: {}
      # If you do want to specify resources, uncomment the following lines, adjust them as necessary.
      # and remove the curly braces after 'resources:'.
#      limits:
#        cpu: 1000m
#        memory: 1000Mi
#      requests:
#        cpu: 100m
#        memory: 100Mi

devices:
  awsneuron:
    customresources:
      - aws.amazon.com/neuron
      - aws.amazon.com/neuroncore
  kunlun:
    enabled: true
    customresources:
      - kunlunxin.com/xpu
  enflame:
    enabled: true
    customresources:
      - enflame.com/vgcu
      - enflame.com/vgcu-percentage
  mthreads:
    enabled: true
    customresources:
      - mthreads.com/vgpu
  nvidia:
    gpuCorePolicy: default
    libCudaLogLevel: 1
  ascend:
    enabled: false
    image: ""
    imagePullPolicy: IfNotPresent
    extraArgs: []
    nodeSelector:
      ascend: "on"
    tolerations: []
    customresources:
      - huawei.com/Ascend910A
      - huawei.com/Ascend910A-memory
      - huawei.com/Ascend910B2
      - huawei.com/Ascend910B2-memory
      - huawei.com/Ascend910B
      - huawei.com/Ascend910B-memory
      - huawei.com/Ascend910B4
      - huawei.com/Ascend910B4-memory
      - huawei.com/Ascend310P
      - huawei.com/Ascend310P-memory

