{{/* vim: set filetype=mustache: */}}

{{/*
Create a default fully qualified app name.
We truncate at 63 chars because some Kubernetes name fields are limited to this (by the DNS naming spec).
If release name contains chart name it will be used as a full name.
*/}}
{{- define "dynamia.licenseServer.fullname" -}}
{{- printf "%s" (include "common.names.fullname" .) | trunc 63 | trimSuffix "-" -}}
{{- end }}

{{/*
Return the proper image name
*/}}
{{- define "dynamia.licenseServer.image" -}}
{{ include "common.images.image" (dict "imageRoot" .Values.image "global" .Values.global) }}
{{- end -}}

{{/*
Return the proper image Registry Secret Names
*/}}
{{- define "dynamia.licenseServer.imagePullSecrets" -}}
{{ include "common.images.pullSecrets" (dict "images" (list .Values.image) "global" .Values.global) }}
{{- end -}}

{{- define "dynamia.mysql.dsn" -}}
{{- if .Values.mysql.enabled }}
     {{- printf "root:%s@tcp(%s-mysql.%s.svc.cluster.local:3306)/%s?charset=utf8mb4&parseTime=true&loc=Local" .Values.mysql.auth.rootPassword .Release.Name .Release.Namespace .Values.mysql.auth.database }}
{{- else }}
     {{- required "mysql dsn is required" .Values.mysqlDSN }}
{{- end -}}
{{- end -}}

{{- define "dynamia.mysql.image" -}}
  {{- include "common.images.image" (dict "imageRoot" .Values.mysql.image "global" .Values.global) -}}
{{- end -}}

{{- define "dynamia.mysql.persistence.matchNode" -}}
  {{- if and (not (empty .Values.persistenceMatchNode)) (not (eq .Values.persistenceMatchNode "None")) }}
    {{- .Values.persistenceMatchNode -}}
  {{- else if not (eq .Values.persistenceMatchNode "None") }}
    {{- required "Please set parameter persistenceMatchNode, if PV resources are not required, set it to None!" .Values.persistenceMatchNode -}}
  {{- end }}
{{- end -}}
