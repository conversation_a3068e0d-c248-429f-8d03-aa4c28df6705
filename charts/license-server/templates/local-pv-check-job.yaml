{{- if and (include "dynamia.mysql.persistence.matchNode" .) (.Values.mysql.enabled) -}}
apiVersion: batch/v1
kind: Job
metadata:
  name: {{ include "dynamia.licenseServer.fullname" . }}-check-local-pv
  namespace: {{ .Release.Namespace }}
  labels: {{ include "common.labels.standard" . | nindent 4 }}
  annotations:
    "helm.sh/hook": pre-install,pre-upgrade
    "helm.sh/hook-weight": "0"
    "helm.sh/hook-delete-policy": hook-succeeded
spec:
  ttlSecondsAfterFinished: 600
  template:
    metadata:
      labels:
        app: {{ include "dynamia.licenseServer.fullname" . }}
        job: check-node-local-pv-dir
    spec:
      restartPolicy: Never
      nodeName: {{ include "dynamia.mysql.persistence.matchNode" . }}
      containers:
      - name: check-dir
        image: {{ include "dynamia.mysql.image" . }}
        command: ['sh', '-c', 'stat /bitnami/mysql']
        volumeMounts:
        - name: pv-dir
          mountPath: /bitnami/mysql
      volumes:
      - name: pv-dir
        hostPath:
          path: /var/local/dynamia/mysql
      tolerations:
      - key: "node-role.kubernetes.io/master"
        operator: "Exists"
        effect: "NoSchedule"
{{- end -}}

