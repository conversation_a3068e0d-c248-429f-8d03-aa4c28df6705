{{- if and (include "dynamia.mysql.persistence.matchNode" .) (.Values.mysql.enabled) -}}
apiVersion: v1
kind: PersistentVolume
metadata:
  name: {{ include "dynamia.licenseServer.fullname" . }}
  labels: {{- include "common.labels.standard" . | nindent 4 }}
{{- if .Values.mysql.primary.persistence.selector.matchLabels }}
  {{- include "common.tplvalues.render" ( dict "value" .Values.mysql.primary.persistence.selector.matchLabels "context" $ ) | nindent 4 }}
{{- end }}
spec:
  capacity:
    storage: {{ .Values.mysql.primary.persistence.size }}
  volumeMode: Filesystem
  accessModes:
  - ReadWriteOnce
  persistentVolumeReclaimPolicy: Retain
  local:
    path: /var/local/dynamia/mysql
  nodeAffinity:
    required:
      nodeSelectorTerms:
      - matchExpressions:
        {{- if (include "dynamia.mysql.persistence.matchNode" .) }}
        - key: kubernetes.io/hostname
          operator: In
          values:
          - {{ include "dynamia.mysql.persistence.matchNode" . }}
        {{- end }}
{{- end -}}
