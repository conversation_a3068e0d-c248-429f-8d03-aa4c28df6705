apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "dynamia.licenseServer.fullname" . }}
  namespace: {{ .Release.Namespace }}
  labels:
    control-plane: {{ include "dynamia.licenseServer.fullname" . }}
    {{- if .Values.labels }}
    {{- include "common.tplvalues.render" ( dict "value" .Values.labels "context" $ ) | nindent 4 }}
    {{- end }}
spec:
  replicas: {{ .Values.replicaCount }}
  selector:
    matchLabels:
      control-plane: {{ include "dynamia.licenseServer.fullname" . }}
  template:
    metadata:
      {{- if .Values.podAnnotations }}
      annotations:
      {{- include "common.tplvalues.render" (dict "value" .Values.podAnnotations "context" $) | nindent 8 }}
      {{- end }}
      labels:
        control-plane: {{ include "dynamia.licenseServer.fullname" . }}
        {{- if .Values.podLabels }}
        {{- include "common.tplvalues.render" ( dict "value" .Values.podLabels "context" $ ) | nindent 8 }}
        {{- end }}
    spec:
      {{- include "dynamia.licenseServer.imagePullSecrets" . | nindent 6 }}
      containers:
        - name: {{ include "dynamia.licenseServer.fullname" . }}
          image: {{ include "dynamia.licenseServer.image" . }}
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          command:
            - /bin/license-server
            - --insecure-port=8000
            - --encrypt-key-path=/etc/license-server/public.key
            - --decrypt-key-path=/etc/license-server/private.key
            - --mysql-dsn={{ include "dynamia.mysql.dsn" . }}
          {{- if .Values.resources }}
          resources: {{- toYaml .Values.resources | nindent 12 }}
          {{- end }}
          {{- if .Values.livenessProbe.enabled}}
          livenessProbe: {{- omit .Values.livenessProbe "enabled" | toYaml | nindent 12 }}
          {{- end }}
          {{- if .Values.readinessProbe.enabled}}
          readinessProbe: {{- omit .Values.readinessProbe "enabled" | toYaml | nindent 12 }}
          {{- end }}
          volumeMounts:
            - name: license-server-keys
              mountPath: /etc/license-server
      {{- if .Values.affinity }}
      affinity: {{- include "common.tplvalues.render" (dict "value" .Values.affinity "context" $) | nindent 8 }}
      {{- end }}
      {{- if .Values.nodeSelector }}
      nodeSelector: {{- include "common.tplvalues.render" (dict "value" .Values.nodeSelector "context" $) | nindent 8 }}
      {{- end }}
      {{- if .Values.tolerations }}
      tolerations: {{- include "common.tplvalues.render" (dict "value" .Values.tolerations "context" $) | nindent 8 }}
      {{- end }}
      volumes:
        - name: license-server-keys
          secret:
            secretName: {{ .Release.Name }}-keys
