## Default values for charts.
## This is a YAML-formatted file.
## Declare variables to be passed into your templates.

## @section Global configuration
## @param global.imageRegistry Global Docker image registry
## @param global.imagePullSecrets Global Docker image pull secrets
global:
  ## @param global.imageRegistry Global Docker image registry
  imageRegistry: ""
  ## E.g.
  ## imagePullSecrets:
  ##   - myRegistryKeySecretName
  ## @param global.imagePullSecrets Global Docker image pull secrets
  imagePullSecrets: []

## @section controllerManager configuration
## @param labels controllerManager labels
labels: {}

## @param replicaCount controllerManager target replica count
replicaCount: 1

## @param podAnnotations controllerManager pod annotations
podAnnotations: {}

## @param podLabels controllerManager pod labels
podLabels: {}

## @param global.persistence.matchNode define the nodeAffinity of pv.
## if PV resources are not required, set it to None!
## if defined, will create the pv on the node.
persistenceMatchNode: ""

mysqlDSN: ""

## @param image.registry license server image registry
## @param image.repository license server image repository
## @param image.tag license server image tag (immutable tags are recommended)
## @param image.pullPolicy license server image pull policy
## @param image.pullSecrets Specify docker-registry secret names as an array
image:
  registry: docker.io
  repository: dynamia-ai/license-server
  tag: "v0.0.1"
  ## Specify a imagePullPolicy
  ## Defaults to 'Always' if image tag is 'latest', else set to 'IfNotPresent'
  ## ref: https://kubernetes.io/docs/user-guide/images/#pre-pulling-images
  ##
  pullPolicy: IfNotPresent
  ## Optionally specify an array of imagePullSecrets.
  ## Secrets must be manually created in the namespace.
  ## Example:
  ## pullSecrets:
  ##   - myRegistryKeySecretName
  ##
  pullSecrets: []

## @param resources controllerManager resource requests and limits
resources: {}
  # If you do want to specify resources, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
  # limits:
  #   cpu: 100m
  #   memory: 128Mi
  # requests:
  #   cpu: 100m
  #   memory: 128Mi
## @param nodeSelector controllerManager node labels for pod assignment
nodeSelector: {}

## @param affinity controllerManager affinity settings
affinity: {}

## @param tolerations controllerManager tolerations for pod assignment
tolerations: {}

## @param livenessProbe.enabled Enable livenessProbe on Kafka containers
livenessProbe:
  enabled: false

## @param readinessProbe.enabled Enable readinessProbe on Kafka containers
readinessProbe:
  enabled: false

## @section mysql Parameters
##
## mysql properties
##
mysql:
  enabled: true
  ## Properties for using an existing mysql installation
  ## @param mysql.image.registry mysql image registry
  ## @param mysql.image.repository mysql image repository
  ## @param mysql.image.tag mysql image tag (immutable tags are recommended)
  ## @param mysql.image.pullPolicy mysql image pull policy
  ## @param mysql.image.pullSecrets Specify image pull secrets
  ##
  image:
    registry: docker.io
    repository: bitnami/mysql
    tag: 8.0.28-debian-10-r23
    ## Specify a imagePullPolicy
    ## Defaults to 'Always' if image tag is 'latest', else set to 'IfNotPresent'
    ##
    pullPolicy: IfNotPresent
    ## Optionally specify an array of imagePullSecrets.
    ## Secrets must be manually created in the namespace.
    ## Example:
    ## pullSecrets:
    ##   - myRegistryKeySecretName
    ##
    pullSecrets: []
  auth:
    ## @param auth.rootPassword Password for the `root` user. Ignored if existing secret is provided
    ##
    rootPassword: "dangerous0"
    ## @param auth.database Name for a custom database to create
    ##
    database: dynamia
    ## @param auth.username Name for a custom user to create
    ##
    username: ""
    ## @param auth.password Password for the new user. Ignored if existing secret is provided
    ##
    password: ""
  ## @primary config
  primary:
    ## Enable persistence using Persistent Volume Claims
    ##
    persistence:
      ## @param primary.persistence.enabled Enable persistence on MySQL primary replicas using a `PersistentVolumeClaim`. If false, use emptyDir
      ##
      enabled: true
      ## @param primary.persistence.size MySQL primary persistent volume size
      ##
      size: 5Gi
