# Gpu-pod1 and gpu-pod2 will NOT share the same GPU
apiVersion: v1
kind: Pod
metadata:
  name: gpu-pod1
spec:
  containers:
    - name: ubuntu-container
      image: ubuntu:18.04
      command: ["bash", "-c", "sleep 86400"]
      resources:
        limits:
          nvidia.com/gpu: 1 # declare how many physical GPUs the pod needs
---
apiVersion: v1
kind: Pod
metadata:
  name: gpu-pod2
spec:
  containers:
    - name: ubuntu-container
      image: ubuntu:18.04
      command: ["bash", "-c", "sleep 86400"]
      resources:
        limits:
          nvidia.com/gpu: 1 # declare how many physical GPUs the pod needs