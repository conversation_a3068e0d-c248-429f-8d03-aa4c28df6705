apiVersion: v1
kind: Pod
metadata:
  name: gpu-pod
  annotations:
    # You can run command: kubectl get node $node -o jsonpath='{.metadata.annotations.hami\.io/node-nvidia-register}' to get registered gpu info
    # The full GPU type name is like NVIDIA-NVIDIA A100, while the short name is like A100
    nvidia.com/use-gputype: "A100,V100" # Specify the card type for this job, use comma to seperate, will launch job on specified card
    # In this example, we want to run this job on A100 or V100
spec:
  containers:
    - name: ubuntu-container
      image: ubuntu:18.04
      command: ["bash", "-c", "sleep 86400"]
      resources:
        limits:
          nvidia.com/gpu: 2 # declare how many physical GPUs the pod needs
