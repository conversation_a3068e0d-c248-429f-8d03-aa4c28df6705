apiVersion: v1
kind: Pod
metadata:
  name: gpu-pod
spec:
  containers:
    - name: ubuntu-container
      image: ubuntu:18.04
      command: ["bash", "-c", "sleep 86400"]
      resources:
        limits:
          nvidia.com/gpu: 2 # declare how many physical GPUs the pod needs
          nvidia.com/gpumem-percentage: 50 # identifies 50% GPU memory each physical GPU allocates to the pod （Optional,Integer）
          nvidia.com/gpucores: 30 # identifies 30% GPU GPU core each physical GPU allocates to the pod （Optional,Integer)
