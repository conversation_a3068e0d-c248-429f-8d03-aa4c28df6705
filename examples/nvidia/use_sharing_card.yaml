# Gpu-pod1 and gpu-pod2 could share the same GPU
apiVersion: v1
kind: Pod
metadata:
  name: gpu-pod1
spec:
  containers:
    - name: ubuntu-container
      image: ubuntu:18.04
      command: ["bash", "-c", "sleep 86400"]
      resources:
        limits:
          nvidia.com/gpu: 1 # declare how many physical GPUs the pod needs
          nvidia.com/gpumem-percentage: 40 # identifies 40% GPU memory each physical GPU allocates to the pod （Optional,Integer)
          nvidia.com/gpucores: 60 # identifies 60% GPU GPU core each physical GPU allocates to the pod （Optional,Integer)
---
apiVersion: v1
kind: Pod
metadata:
  name: gpu-pod2
spec:
  containers:
    - name: ubuntu-container
      image: ubuntu:18.04
      command: ["bash", "-c", "sleep 86400"]
      resources:
        limits:
          nvidia.com/gpu: 1 # declare how many physical GPUs the pod needs
          nvidia.com/gpumem-percentage: 60 # identifies 60% GPU memory each physical GPU allocates to the pod （Optional,Integer)
          nvidia.com/gpucores: 40 # identifies 40% GPU GPU core each physical GPU allocates to the pod （Optional,Integer)
