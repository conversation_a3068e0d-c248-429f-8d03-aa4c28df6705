apiVersion: apps/v1
kind: Deployment
metadata:
  name: license-server
  namespace: hami-system
spec:
  replicas: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: license-server
  template:
    metadata:
      labels:
        app.kubernetes.io/name: license-server
    spec:
      containers:
        - name: license-server
          image: docker.io/dynamic-ai/license-server:v0.0.1
          command: 
            - /bin/license-server
            - --insecure-port=8000
            - --encrypt-key-path=/etc/license-server/public.key
            - --decrypt-key-path=/etc/license-server/private.key
          ports:
            - containerPort: 8000
          volumeMounts:
            - name: license-server-keys
              mountPath: /etc/license-server
      volumes:
        - name: license-server-keys
          secret:
            secretName: license-server-keys

---
apiVersion: v1
kind: Service
metadata:
  name: license-server
  namespace: hami-system
spec:
  selector:
    app.kubernetes.io/name: license-server
  ports:
    - port: 8000
      targetPort: 8000

---
apiVersion: v1
kind: Secret
metadata:
  name: license-server-keys
  namespace: hami-system
type: Opaque
data:
  public.key: |
    LS0tLS1CRUdJTiBQVUJMSUMgS0VZLS0tLS0KTUlJQklqQU5CZ2txaGtpRzl3MEJBUUVGQUFPQ0FROEFNSUlCQ2dLQ0FRRUF3R0hlSmFrbU1tWUQ1UnR1TUFJRApoYlZEZXBCcUZIMThEbUhkeHVLTXpIWVR3eCtnYzhucWdjWlM3RGJyNXRqdHNkQjlVb1lTSE1aeFVIc0xLTHhTCk5KRzNEdE16bXZQRSthVS91R1hFQ0JRYlIrRVBhREhOZ2FQeGpzcE1lV1dzOVA2MW5iRnBXOUVBbGNsYmNxZkQKVGJQbHRlZUcxUndITlB0c2JmNnVCR2VIU1dFUFN4UU85cmJlaHVFU3F6NldMT3Q0REFQYThqYVp2WUIveEJyQgpYZ0x6Vk1neWRoam5pV2I5aFByVE9pRU0rQXF3TGZyUEExS3YzM01nN0VyemZyZ0h2SmQ5VFBtWVVKWkZKdEdPCnFocjdoQW9PL0NRbU8wZ0RUNzQ4YmlOdWI0enlFaHhNdVVlSGoybVN5aGtJZUtGQkVDeVhPUVhFRGloYWtGQ0kKaXdJREFRQUIKLS0tLS1FTkQgUFVCTElDIEtFWS0tLS0tCg==
  private.key: |
    ****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

---
apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: license-server
  namespace: hami-system
spec:
  parentRefs:
  - group: gateway.networking.k8s.io
    kind: Gateway
    name: hami
    namespace: hami-system
  rules:
  - backendRefs:
    - group: ""
      kind: Service
      name: license-server
      port: 8000
      weight: 1
    matches:
    - path:
        type: PathPrefix
        value: /api/dynamia.ai
