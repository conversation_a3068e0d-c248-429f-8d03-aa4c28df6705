name: Call e2e test

on:
  workflow_call:
    inputs:
      ref:
        description: 'Reference id to run tests'
        required: true
        type: string
      type:
        description: 'E2E type'
        required: true
        type: string
        default: pullrequest

jobs:
  e2e-test:
    strategy:
      matrix:
        include:
          - device: nvidia
            type: tesla-p4
#          - device: nvidia
#            type: rtx-4090
#          - device: huawei
#            type: ascend-910b
    runs-on: [ "${{ matrix.device }}", "${{ matrix.type }}" ]
    environment: ${{ matrix.device }}
    env:
      E2E_TYPE: ${{ inputs.type }}
      HAMI_VERSION: ${{ inputs.ref }}
    steps:
      - name: checkout code
        uses: actions/checkout@v5

      - name: install Go
        uses: actions/setup-go@v5
        with:
          go-version: "1.21"

      - name: setup e2e env
        run: |
          make e2e-env-setup

      - name: download hami helm
        if: inputs.type == 'pullrequest'
        uses: actions/download-artifact@v5
        with:
          name: chart_package_artifact
          path: charts/

      - name: download hami image
        if: inputs.type == 'pullrequest'
        uses: actions/download-artifact@v5
        with:
          name: hami-image
          path: ./image

      - name: load e2e image
        if: inputs.type == 'pullrequest'
        run: |
          echo "Loading Docker image from image.tar..."
          if [ -z "${VSPHERE_GPU_VM_IP}" ]; then
            echo "Error: VSPHERE_GPU_VM_IP is not defined!"
            exit 1
          fi
          scp ./image/image.tar root@$VSPHERE_GPU_VM_IP:/home/
          ssh root@$VSPHERE_GPU_VM_IP "nerdctl load -i /home/<USER>"
          ssh root@$VSPHERE_GPU_VM_IP "nerdctl image ls | grep hami"

      - name: deploy hami helm
        run: |
          make helm-deploy

      - name: e2e test
        run: |
          make e2e-test
