name: CI Workflow
on:
  # Run this workflow every time a new commit pushed to upstream/fork repository.
  # Run workflow on fork repository will help contributors find and resolve issues before sending a PR.
  pull_request:
  push:
    # Exclude branches created by <PERSON><PERSON><PERSON><PERSON> to avoid triggering current workflow
    # for PRs initiated by <PERSON><PERSON><PERSON><PERSON>.
    branches-ignore:
      - 'dependabot/**'
permissions:
  contents: read  # for actions/checkout to fetch code

env:
  REGISTRY: docker.io
  IMAGE_REPO: ${{ secrets.IMAGE_REPO || 'projecthami/hami' }}
  IMAGE_REPO_HAMICORE: ${{ secrets.IMAGE_REPO || 'projecthami/hamicore' }}
  IMAGE_ROOT_PATH: docker
  BUILD_PLATFORM: linux/arm64,linux/amd64
  REGISTER_USER: ${{ github.actor }}
  REGISTER_PASSWORD: ${{ secrets.GITHUB_TOKEN }}

jobs:
  lint:
    name: lint
    runs-on: ubuntu-22.04
    steps:
      - name: checkout code
        uses: actions/checkout@v5
      - name: install Go
        uses: actions/setup-go@v5
        with:
          go-version-file: go.mod
      - name: verify license
        run: hack/verify-license.sh
      - name: go tidy
        run: make tidy
      - name: golangci-lint
        uses: golangci/golangci-lint-action@v7
        with:
          version: v2.0
      - name: import alias
        run: hack/verify-import-aliases.sh

  get_info:
    name: get_info
    runs-on: ubuntu-22.04
    outputs:
      version: ${{ steps.parse_version.outputs.version }}
      e2e_run: ${{ steps.parse_code.outputs.e2e_run }}
    steps:
      - name: Checkout
        uses: actions/checkout@v5
        with:
          fetch-depth: 3

      - name: Parse the version
        id: parse_version
        run: |
          value=$(make -f Makefile.defs print-version)
          suffix="$(git rev-parse --short HEAD)"
          tag="${value}-${suffix}"
          echo "Generated tag: ${tag}"
          echo "version=${tag}" >> $GITHUB_OUTPUT

      # Check the code diff, ignore the docs and examples change
      - name: Parse the code diff
        id: parse_code
        run: |
          pr_commit=${{ github.event.pull_request.base.sha }}
          base_commit=${{ github.sha }}
          echo "PR commit: ${pr_commit}"
          echo "Git log: $(git log ${pr_commit})"
          echo "Base commit: ${base_commit}"
          echo "Git log: $(git log ${base_commit})"
          changed_files=$(git diff --name-only ${pr_commit} ${base_commit} -- . ':(exclude)docs/*' ':(exclude)examples/*' ':(exclude)*.md')
          echo "Changed files: ${changed_files}"
          if [ -n "$changed_files" ]; then
            echo "Will run e2e-test"
            echo "e2e_run=true" >> $GITHUB_OUTPUT
          else
            echo "Won't run e2e-test"
            echo "e2e_run=false" >> $GITHUB_OUTPUT
          fi

  test:
    name: Unit test
    needs: [ lint, get_info ] # rely on lint & get_info successful completion
    runs-on: ubuntu-22.04
    if: needs.get_info.outputs.e2e_run == 'true' || github.ref == 'refs/heads/master'
    steps:
      - name: Checkout code
        uses: actions/checkout@v5
      - name: Checkout submodule
        uses: Mushus/checkout-submodule@v1.0.1
        with:
          basePath: # optional, default is .
          submodulePath: libvgpu
      - name: Install Go
        uses: actions/setup-go@v5
        with:
          go-version-file: go.mod
      - run: make tidy
      - run: make test
      - name: Upload coverage to Codecov
        # Prevent running from the forked repository that doesn't need to upload coverage.
        # In addition, running on the forked repository would fail as missing the necessary secret.
        if: ${{ github.repository == 'Project-HAMi/HAMi' }}
        uses: codecov/codecov-action@v5
        with:
          # Even though token upload token is not required for public repos,
          # but adding a token might increase successful uploads as per:
          # https://community.codecov.com/t/upload-issues-unable-to-locate-build-via-github-actions-api/3954
          token: ${{secrets.CODECOV_TOKEN}}
          files: ./_output/coverage/coverage.out
          flags: unittests
          fail_ci_if_error: false
          verbose: true

  build:
    name: Compile
    needs: [ test, get_info ]
    runs-on: ubuntu-22.04
    if: needs.get_info.outputs.e2e_run == 'true'
    steps:
      - uses: actions/checkout@master
      - name: Free disk space
        # https://github.com/actions/virtual-environments/issues/709
        run: |
          echo "=========original CI disk space"
          df -h
          sudo rm -rf "/usr/local/share/boost"
          sudo rm -rf "$AGENT_TOOLSDIRECTORY"
          echo "=========after clean up, the left CI disk space"
          df -h

      - name: Checkout code
        uses: actions/checkout@v5
        with:
          fetch-depth: 0

      - name: Checkout submodule
        uses: Mushus/checkout-submodule@v1.0.1
        with:
          basePath: # optional, default is .
          submodulePath: libvgpu

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
        with:
          driver-opts: image=moby/buildkit:master

      - name: Extract metadata (tags, labels) for Docker
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_REPO }}

      - name: Build & Pushing hami image
        uses: docker/build-push-action@v6.18.0
        with:
          context: .
          file: ${{ env.IMAGE_ROOT_PATH }}/Dockerfile
          labels: ${{ needs.get_info.outputs.version }}
          build-args: |
            VERSION=${{ needs.get_info.outputs.version }}
            GOLANG_IMAGE=golang:1.24.4-bullseye
            NVIDIA_IMAGE=nvidia/cuda:12.2.0-devel-ubuntu20.04
            DEST_DIR=/usr/local
          tags: ${{ env.REGISTRY }}/${{ env.IMAGE_REPO }}:${{ needs.get_info.outputs.version }}
          push: false
          load: true
          github-token: ${{ env.REGISTER_PASSWORD }}

      - name: List images
        run: |
          docker images

      - name: Save Docker image to image.tar
        run: |
          docker save ${{ env.REGISTRY }}/${{ env.IMAGE_REPO }}:${{ needs.get_info.outputs.version }} -o image.tar

      - name: Upload image.tar as artifact
        uses: actions/upload-artifact@v4
        with:
          name: hami-image
          path: image.tar
          retention-days: 5
          if-no-files-found: error

  # packages tgz from /charts of original branch, deploy to /charts of target branch
  package_chart:
    runs-on: ubuntu-22.04
    needs: [ test, get_info ]
    if: needs.get_info.outputs.e2e_run == 'true'
    env:
      HELM_VERSION: v3.8.1
    steps:
      - name: Checkout
        uses: actions/checkout@v5
        with:
          fetch-depth: 0
          ref: ${{ needs.get_ref.outputs.ref }}

      - name: Configure Git
        run: |
          git config user.name "$GITHUB_ACTOR"
          git config user.email "$<EMAIL>"

      - name: Install Helm
        uses: azure/setup-helm@v4
        with:
          version: ${{ env.HELM_VERSION }}

      - name: Lint helm chart
        run: |
          make lint_chart

      - name: Package Chart
        continue-on-error: false
        env:
          VERSION: ${{ needs.get_info.outputs.version }}
        run: |
          cd charts
          make clean
          make
          if ! ls *.tgz &>/dev/null ; then
            echo "failed to generate chart"
            exit 1
          fi
          cd ..
          mkdir -p tmp
          mv charts/*.tgz tmp

      - name: Upload Artifact
        uses: actions/upload-artifact@v4.4.3
        with:
          name: chart_package_artifact
          path: tmp/*
          retention-days: 5
          if-no-files-found: error

  # execute a full e2e test when hami code merge
  e2e_test:
    uses: ./.github/workflows/call-e2e.yaml
    needs: [ package_chart, get_info, build ]
    if: needs.get_info.outputs.e2e_run == 'true'
    with:
      ref: ${{ needs.get_info.outputs.version }}
      type: "pullrequest"
