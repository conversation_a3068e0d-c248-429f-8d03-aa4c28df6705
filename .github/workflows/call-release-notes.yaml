name: save release notes


on:
  workflow_call:
    inputs:
      ref:
        required: true
        type: string

permissions: write-all

jobs:
  generate-release-notes:
    runs-on: ubuntu-latest
    steps:
    - name: Download Artifact
      uses: actions/download-artifact@v5
      with:
        name: chart_package_artifact
        path: charts/
    # generate release notes for the new release branch
    - name: upload chart tgz to release
      if: startsWith(github.ref, 'refs/tags/')
      uses: softprops/action-gh-release@v2
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        files: "charts/*.tgz"
    - name: Checkout
      uses: actions/checkout@v5
      with:
        fetch-depth: 0
        ref: master

    - name: Create release notes markdown
      run: |
        echo "something need to fix https://github.com/wawa0210/HAMi/actions/runs/11008166155/job/30565367256"
        # 1. get pre-release id
        # pre_release_id=$(curl -H "Accept: application/vnd.github+json" -H "Authorization: Bearer ${{ secrets.GITHUB_TOKEN }}" https://api.github.com/repos/${GITHUB_REPOSITORY}/releases | yq '.[] | select(.prerelease == true) | .id')
        # echo "pre_release_id: ${pre_release_id}"

        # 2. update release status from pre-release to latest-release
        # curl -L \
        #   -X PATCH \
        #   -H "Accept: application/vnd.github+json" \
        #   -H "Authorization: Bearer ${{ secrets.GITHUB_TOKEN }}" \
        #   -H "X-GitHub-Api-Version: 2022-11-28" \
        #   https://api.github.com/repos/${GITHUB_REPOSITORY}/releases/${pre_release_id} \
        #   -d '{"draft":false,"prerelease":false,"make_latest":"true"}'

        # 3. generate release notes content for new release
        #  timeout need to fix, ref https://github.com/wawa0210/HAMi/actions/runs/11008166155/job/30565367256
        # latest_tag=${GITHUB_REF#refs/*/}
        # curl -o result.json \
        #   -H "Accept: application/vnd.github+json" \
        #   -H "Authorization: Bearer ${{ secrets.GITHUB_TOKEN }}" \
        #   https://api.github.com/repos/${GITHUB_REPOSITORY}/releases/tags/${latest_tag}
        
        # cat result.json | yq '.body' > docs/CHANGELOG/${latest_tag}.md

        # rm -rf result.json


    # - name: Push release notes
    #   id: push_directory
    #   uses: cpina/github-action-push-to-another-repository@v1.7.2
    #   env:
    #     API_TOKEN_GITHUB: ${{ secrets.API_TOKEN_GITHUB }}
    #   with:
    #     source-directory: .
    #     destination-github-username: ${{ github.repository_owner }}
    #     destination-repository-name: hami
    #     user-email: <EMAIL>
    #     commit-message: upgrade ORIGIN_COMMIT from $GITHUB_REF
    #     target-branch: master
