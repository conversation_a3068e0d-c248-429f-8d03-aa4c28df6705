name: Call Release Helm Charts

# must set branch 'github_pages' as github page
# this workflow will create the tgz from "/charts/*" of branch main,
# and deploy to "/charts" of branch "github_pages"
# and on branch "github_pages", update '/index.yaml' for '/charts/*.tgz'

env:
  HELM_VERSION: v3.8.1
  MERGE_BRANCH: gh-pages
on:
  workflow_call:
    inputs:
      ref:
        required: true
        type: string
      submit:
        required: true
        type: string
    outputs:
      artifact:
        description: "name of artifact"
        value: chart_package_artifact
  workflow_dispatch:
    inputs:
      ref:
        description: 'tag, sha, branch'
        required: true
        default: v1.0.0

permissions: write-all

jobs:
  get_ref:
    runs-on: ubuntu-latest
    outputs:
      ref: ${{ env.REF }}
      submit: ${{ env.SUBMIT }}
    steps:
      - name: Get Original Ref
        id: get_original_ref
        run: |
          if ${{ inputs.ref != '' }} ; then
              echo "call by workflow_call"
              echo "REF=${{ inputs.ref }}" >> $GITHUB_ENV
              echo "SUBMIT=${{ inputs.submit }}" >> $GITHUB_ENV
          elif ${{ github.event_name == 'workflow_dispatch' }} ; then
              echo "call by self workflow_dispatch"
              echo "REF=${{ inputs.ref }}" >> $GITHUB_ENV
              echo "SUBMIT=true" >> $GITHUB_ENV
          else
              echo "unexpected event: ${{ github.event_name }}"
              exit 1
          fi

  # packages tgz from /charts of original branch, deploy to /charts of target branch
  package_chart:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v5
        with:
          fetch-depth: 0
          ref: ${{ needs.get_ref.outputs.ref }}
      - name: Configure Git
        run: |
          git config user.name "$GITHUB_ACTOR"
          git config user.email "$<EMAIL>"
      - name: Install Helm
        uses: azure/setup-helm@v4
        with:
          version: ${{ env.HELM_VERSION }}

      - name: Lint helm chart
        run: |
          make lint_chart
      - name: Package Chart
        continue-on-error: false
        run: |
          cd charts
          make clean
          make
          if ! ls *.tgz &>/dev/null ; then
            echo "failed to generate chart"
            exit 1
          fi
          cd ..
          mkdir -p tmp
          mv charts/*.tgz tmp
      - name: Upload Artifact
        uses: actions/upload-artifact@v4.5.0
        with:
          name: chart_package_artifact
          path: tmp/*
          retention-days: 1
          if-no-files-found: error

  # update /index.yaml in the target branch
  update_githubpage:
    runs-on: ubuntu-latest
    needs: [package_chart, get_ref]
    if: ${{ needs.get_ref.outputs.submit == 'true' }}
    steps:
      - name: Get Base Chart URL
        id: get_base_url
        run: |
          name=${{ github.repository }}
          proj=${name#*/}
          url=https://${{ github.repository_owner }}.github.io/${proj}
          echo "URL=${url}" >> $GITHUB_ENV

      - name: Checkout Code
        uses: actions/checkout@v5
        with:
          ref: ${{ env.MERGE_BRANCH }}
          persist-credentials: "true"

      - name: Download Artifact
        uses: actions/download-artifact@v5
        with:
          name: chart_package_artifact
          path: charts/

      - name: Update Chart Yaml
        run: |
          helm repo index  ./charts  --url ${{ env.URL }}/charts
          mv ./charts/index.yaml ./index.yaml
      - name: update helm release
        id: push_directory
        uses: cpina/github-action-push-to-another-repository@v1.7.2
        env:
          API_TOKEN_GITHUB: ${{ secrets.API_TOKEN_GITHUB }}
        with:
          source-directory: .
          destination-github-username: ${{ github.repository_owner }}
          destination-repository-name: hami
          user-email: <EMAIL>
          commit-message: sync ORIGIN_COMMIT from $GITHUB_REF
          target-branch: ${{ env.MERGE_BRANCH }}
