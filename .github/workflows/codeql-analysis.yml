# For most projects, this workflow file will not need changing; you simply need
# to commit it to your repository.
#
# You may wish to alter this file to override the set of languages analyzed,
# or to provide custom queries or build logic.
name: "CodeQL"

on:
  workflow_dispatch:
  push:
    branches: ["master","dev"]
    paths-ignore:
      - "**/*.json"
      - "**/*.md"
      - "**/*.txt"
      - "**/*.yml"
  schedule:
    - cron: "0 4 * * 6"

permissions:
  security-events: write
  # required to fetch internal or private CodeQL packs
  packages: read

  # only required for workflows in private repositories
  actions: read
  contents: read

jobs:
  analyze:
    name: Analyze
    runs-on: ubuntu-latest
    if: github.repository == 'Project-HAMi/HAMi'

    strategy:
      fail-fast: false
      matrix:
        language: ["go"]

    steps:
      - name: Checkout repository
        uses: actions/checkout@v5
      - name: Checkout submodule
        uses: Mushus/checkout-submodule@v1.0.1
        with:
          basePath: # optional, default is .
          submodulePath: libvgpu
      - if: matrix.language == 'go'
        name: Set go version
        uses: actions/setup-go@v5
        with:
          go-version-file: go.mod

      # Initializes the CodeQL tools for scanning.
      - name: Initialize CodeQL
        uses: github/codeql-action/init@v3
        with:
          languages: ${{ matrix.language }}
          # If you wish to specify custom queries, you can do so here or in a config file.
          # By default, queries listed here will override any specified in a config file.
          # Prefix the list here with "+" to use these queries and those in the config file.
          # queries: ./path/to/local/query, your-org/your-repo/queries@main

      - name: Perform CodeQL Analysis
        uses: github/codeql-action/analyze@v3
