name: Test self-hosted-runner

on:
  push:
    # Exclude branches created by <PERSON><PERSON><PERSON><PERSON> to avoid triggering current workflow
    # for PRs initiated by <PERSON><PERSON>da<PERSON>.
    branches-ignore:
      - 'dependabot/**'
  pull_request:
    paths:
      - "charts/**"

jobs:
  e2e:
    runs-on: self-hosted
    steps:
      - name: e2e test
        # https://github.com/actions/virtual-environments/issues/709
        run: |
          echo "Need to add e2e test"

