name: Chart Lint

on:
  push:
    # Exclude branches created by <PERSON><PERSON><PERSON><PERSON> to avoid triggering current workflow
    # for PRs initiated by <PERSON>penda<PERSON>.
    branches-ignore:
      - 'dependabot/**'
  pull_request:
    paths:
      - "charts/**"

jobs:
  chart-lint-test:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v5
        with:
          fetch-depth: 0

      - name: Set up Helm
        uses: azure/setup-helm@v4
        with:
          version: v3.7.1
      - name: Lint Chart
        run: |
          make lint_chart
      - name: Check chart version
        run: bash ./hack/verify-chart-version.sh

