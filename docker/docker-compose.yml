version: '3.8'

services:
  postgres:
    image: postgres:15-alpine
    container_name: postgres
    restart: unless-stopped
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: dangerous0
      POSTGRES_DB: dynamia
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - license-network

  license-server:
    image: docker.io/dynamia-ai/license-server:v0.0.1
    container_name: license-server
    restart: unless-stopped
    depends_on:
      postgres:
        condition: service_healthy
    ports:
      - "8000:8000"
    networks:
      - license-network
    command: [
      "/bin/license-server",
      "--insecure-port=8000",
      "--postgres-dsn=**********************************************/dynamia?sslmode=disable",
      "--encrypt-key-path=/etc/license-server/keys/public.key",
      "--decrypt-key-path=/etc/license-server/keys/private.key",
      "--v=2"
    ]

volumes:
  postgres_data:
    driver: local

networks:
  license-network:
    driver: bridge
