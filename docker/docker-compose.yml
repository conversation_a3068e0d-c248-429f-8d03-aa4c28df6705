version: '3.8'

services:
  postgres:
    image: postgres:15-alpine
    container_name: hami-postgres
    restart: unless-stopped
    environment:
      POSTGRES_USER: ${POSTGRES_USER:-postgres}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-dangerous0}
      POSTGRES_DB: ${POSTGRES_DB:-dynamia}
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - postgres_backup:/backup
      - ./init-scripts:/docker-entrypoint-initdb.d:ro
    ports:
      - "${POSTGRES_PORT:-5432}:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-postgres} -d ${POSTGRES_DB:-dynamia}"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    networks:
      - license-network
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 256M
          cpus: '0.25'
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    security_opt:
      - no-new-privileges:true
    user: postgres

  license-server:
    image: docker.io/dynamia-ai/license-server:${LICENSE_SERVER_VERSION:-v0.0.1}
    container_name: hami-license-server
    restart: unless-stopped
    depends_on:
      postgres:
        condition: service_healthy
    environment:
      - LOG_LEVEL=${LOG_LEVEL:-2}
      - POSTGRES_DSN=postgresql://${POSTGRES_USER:-postgres}:${POSTGRES_PASSWORD:-dangerous0}@postgres:5432/${POSTGRES_DB:-dynamia}?sslmode=disable
    volumes:
      - ./keys:/etc/license-server/keys:ro
      - license_logs:/var/log/license-server
    ports:
      - "${LICENSE_SERVER_PORT:-8000}:8000"
    networks:
      - license-network
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.3'
        reservations:
          memory: 128M
          cpus: '0.1'
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    security_opt:
      - no-new-privileges:true
    read_only: true
    tmpfs:
      - /tmp:noexec,nosuid,size=100m
    command: [
      "/bin/license-server",
      "--insecure-port=8000",
      "--postgres-dsn=$${POSTGRES_DSN}",
      "--encrypt-key-path=/etc/license-server/keys/public.key",
      "--decrypt-key-path=/etc/license-server/keys/private.key",
      "--v=$${LOG_LEVEL}"
    ]

volumes:
  postgres_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${DATA_DIR:-./data}/postgres
  postgres_backup:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${BACKUP_DIR:-./backup}/postgres
  license_logs:
    driver: local

networks:
  license-network:
    driver: bridge
    driver_opts:
      com.docker.network.bridge.name: hami-license-br0
    ipam:
      config:
        - subnet: **********/16
