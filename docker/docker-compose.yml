version: "3.8"

services:
  postgres:
    image: postgres:15-alpine
    container_name: hami-postgres
    restart: unless-stopped
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: dangerous0
      POSTGRES_DB: dynamia
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - postgres_backup:/backup
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d dynamia"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    networks:
      - license-network

  license-server:
    image: docker.io/dynamia-ai/license-server:v0.0.1
    container_name: hami-license-server
    restart: unless-stopped
    depends_on:
      postgres:
        condition: service_healthy
    volumes:
      - ./keys:/etc/license-server/keys:ro
      - license_logs:/var/log/license-server
    ports:
      - "8000:8000"
    networks:
      - license-network
    tmpfs:
      - /tmp:noexec,nosuid,size=100m
    command: [
      "/bin/license-server",
      "--insecure-port=8000",
      "--postgres-dsn=**********************************************/dynamia?sslmode=disable",
      "--encrypt-key-path=/etc/license-server/keys/public.key",
      "--decrypt-key-path=/etc/license-server/keys/private.key",
      "--v=2"
    ]

volumes:
  postgres_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./data/postgres
  postgres_backup:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./backup/postgres
  license_logs:
    driver: local

networks:
  license-network:
    driver: bridge
    driver_opts:
      com.docker.network.bridge.name: hami-license-br0
    ipam:
      config:
        - subnet: **********/16
