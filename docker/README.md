# HAMi License Server Docker Compose

This directory contains the Docker Compose configuration for running the HAMi License Server with PostgreSQL database.

## Quick Start

1. **Initial Setup**
   ```bash
   make setup
   ```

2. **Configure Environment**
   Edit the `.env` file with your configuration:
   ```bash
   vim .env
   ```

3. **Prepare License Keys**
   Place your license server keys in the `keys/` directory:
   ```
   keys/
   ├── public.key
   └── private.key
   ```

4. **Start Services**
   ```bash
   make up
   ```

5. **Check Status**
   ```bash
   make status
   make health
   ```

## Configuration

### Environment Variables

The following environment variables can be configured in the `.env` file:

| Variable | Default | Description |
|----------|---------|-------------|
| `POSTGRES_USER` | `postgres` | PostgreSQL username |
| `POSTGRES_PASSWORD` | `dangerous0` | PostgreSQL password (change this!) |
| `POSTGRES_DB` | `dynamia` | PostgreSQL database name |
| `POSTGRES_PORT` | `5432` | PostgreSQL port |
| `LICENSE_SERVER_VERSION` | `v0.0.1` | License server image version |
| `LICENSE_SERVER_PORT` | `8000` | License server port |
| `LOG_LEVEL` | `2` | Log verbosity level |
| `DATA_DIR` | `./data` | Data directory path |
| `BACKUP_DIR` | `./backup` | Backup directory path |

### Directory Structure

```
docker/
├── docker-compose.yml      # Main compose file
├── .env.example           # Environment variables template
├── .env                   # Your environment variables (created by setup)
├── Makefile              # Management commands
├── README.md             # This file
├── data/                 # Data directory
│   └── postgres/         # PostgreSQL data
├── backup/               # Backup directory
│   └── postgres/         # PostgreSQL backups
├── keys/                 # License server keys
│   ├── public.key        # Public key for encryption
│   └── private.key       # Private key for decryption
├── logs/                 # Application logs
├── init-scripts/         # Database initialization scripts
│   └── 01-init-db.sql    # Initial database setup
└── scripts/              # Management scripts
    ├── backup-db.sh      # Database backup script
    └── restore-db.sh     # Database restore script
```

## Management Commands

Use the Makefile for easy management:

```bash
make help           # Show available commands
make setup          # Initial setup
make up             # Start services
make down           # Stop services
make restart        # Restart services
make logs           # View all logs
make logs-postgres  # View PostgreSQL logs
make logs-license   # View License Server logs
make status         # Show service status
make health         # Check service health
make backup         # Create database backup
make restore BACKUP=file  # Restore from backup
make clean          # Remove everything (with confirmation)
make shell-postgres # Open PostgreSQL shell
make shell-license  # Open License Server shell
```

## Security Features

The optimized configuration includes several security improvements:

- **Environment Variables**: Sensitive data moved to `.env` file
- **Resource Limits**: CPU and memory limits for containers
- **Read-only Filesystem**: License server runs with read-only root filesystem
- **No New Privileges**: Containers cannot escalate privileges
- **User Isolation**: PostgreSQL runs as non-root user
- **Network Isolation**: Custom bridge network with defined subnet

## Backup and Recovery

### Automatic Backups

Create a backup:
```bash
make backup
```

### Manual Backup with Custom Name

```bash
./scripts/backup-db.sh my_backup_name
```

### Restore from Backup

```bash
make restore BACKUP=backup/postgres/backup_20241226_120000.sql.gz
```

### Backup Retention

Backups older than 7 days are automatically cleaned up.

## Monitoring and Logs

### View Logs

```bash
# All services
make logs

# Specific service
make logs-postgres
make logs-license
```

### Health Checks

```bash
make health
```

### Service Status

```bash
make status
```

## Troubleshooting

### Common Issues

1. **Permission Denied on Scripts**
   ```bash
   chmod +x scripts/*.sh
   ```

2. **Port Already in Use**
   Change the ports in `.env` file:
   ```
   POSTGRES_PORT=5433
   LICENSE_SERVER_PORT=8001
   ```

3. **Database Connection Issues**
   Check if PostgreSQL is healthy:
   ```bash
   make logs-postgres
   make health
   ```

4. **License Server Key Issues**
   Ensure keys are properly placed in `keys/` directory and have correct permissions:
   ```bash
   ls -la keys/
   chmod 644 keys/*.key
   ```

### Reset Everything

To completely reset and start fresh:
```bash
make clean
make setup
# Edit .env file
make up
```

## Production Considerations

For production deployment, consider:

1. **Change Default Passwords**: Update `POSTGRES_PASSWORD` in `.env`
2. **Use External Volumes**: Configure persistent storage
3. **Enable SSL**: Configure PostgreSQL SSL and License Server TLS
4. **Monitoring**: Add monitoring and alerting
5. **Backup Strategy**: Implement automated backup to external storage
6. **Resource Limits**: Adjust CPU and memory limits based on load
7. **Network Security**: Use proper firewall rules and network policies
