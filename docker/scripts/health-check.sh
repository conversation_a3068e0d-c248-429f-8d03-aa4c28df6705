#!/bin/bash

# HAMi License Server Health Check Script

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Load environment variables
if [ -f .env ]; then
    export $(cat .env | grep -v '^#' | xargs)
fi

# Default values
LICENSE_SERVER_PORT=${LICENSE_SERVER_PORT:-8000}
POSTGRES_PORT=${POSTGRES_PORT:-5432}

echo "HAMi License Server Health Check"
echo "================================"

# Check if containers are running
echo -n "Checking containers... "
if docker-compose ps | grep -q "Up"; then
    echo -e "${GREEN}✓${NC}"
else
    echo -e "${RED}✗ Containers not running${NC}"
    exit 1
fi

# Check PostgreSQL
echo -n "Checking PostgreSQL... "
if docker-compose exec -T postgres pg_isready -U postgres >/dev/null 2>&1; then
    echo -e "${GREEN}✓${NC}"
else
    echo -e "${RED}✗ PostgreSQL not ready${NC}"
    exit 1
fi

# Check License Server HTTP endpoint
echo -n "Checking License Server... "
if curl -f -s "http://localhost:${LICENSE_SERVER_PORT}/health" >/dev/null 2>&1; then
    echo -e "${GREEN}✓${NC}"
elif curl -f -s "http://localhost:${LICENSE_SERVER_PORT}/" >/dev/null 2>&1; then
    echo -e "${GREEN}✓${NC}"
else
    echo -e "${YELLOW}? License Server HTTP check failed (endpoint may not exist)${NC}"
fi

# Check disk space
echo -n "Checking disk space... "
DISK_USAGE=$(df . | tail -1 | awk '{print $5}' | sed 's/%//')
if [ "$DISK_USAGE" -lt 90 ]; then
    echo -e "${GREEN}✓ (${DISK_USAGE}% used)${NC}"
else
    echo -e "${YELLOW}⚠ High disk usage (${DISK_USAGE}% used)${NC}"
fi

# Check memory usage
echo -n "Checking memory usage... "
if command -v free >/dev/null 2>&1; then
    MEM_USAGE=$(free | grep Mem | awk '{printf "%.0f", $3/$2 * 100.0}')
    if [ "$MEM_USAGE" -lt 90 ]; then
        echo -e "${GREEN}✓ (${MEM_USAGE}% used)${NC}"
    else
        echo -e "${YELLOW}⚠ High memory usage (${MEM_USAGE}% used)${NC}"
    fi
else
    echo -e "${YELLOW}? Memory check not available${NC}"
fi

# Check log file sizes
echo -n "Checking log sizes... "
LOG_SIZE=$(docker system df | grep "Local Volumes" | awk '{print $3}' | sed 's/[^0-9.]//g')
if [ -n "$LOG_SIZE" ] && [ "$(echo "$LOG_SIZE < 1" | bc -l 2>/dev/null || echo 1)" -eq 1 ]; then
    echo -e "${GREEN}✓${NC}"
else
    echo -e "${YELLOW}⚠ Large log volumes${NC}"
fi

echo ""
echo -e "${GREEN}Health check completed${NC}"

# Show container status
echo ""
echo "Container Status:"
docker-compose ps
