-- Initialize database for HAMi License Server
-- This script runs automatically when the PostgreSQL container starts for the first time

-- Create extensions if needed
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create indexes for better performance (add specific indexes based on your schema)
-- Example:
-- CREATE INDEX IF NOT EXISTS idx_licenses_created_at ON licenses(created_at);
-- CREATE INDEX IF NOT EXISTS idx_licenses_status ON licenses(status);

-- Set default timezone
SET timezone = 'UTC';

-- Log initialization
DO $$
BEGIN
    RAISE NOTICE 'HAMi License Server database initialized successfully';
END $$;
