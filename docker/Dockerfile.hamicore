ARG NVIDIA_IMAGE=nvidia/cuda:12.2.0-devel-ubuntu20.04

FROM $NVIDIA_IMAGE AS nvbuild
COPY ./libvgpu /libvgpu
WORKDIR /libvgpu
ENV DEBIAN_FRONTEND=noninteractive
RUN apt-get -y update; apt-get -y install cmake
RUN bash ./build.sh

FROM nvidia/cuda:12.6.3-base-ubi8
RUN rm -rf /usr/local/cuda-12.6/compat/libcuda.so*
ENV NVIDIA_DISABLE_REQUIRE="true"
ENV NVIDIA_VISIBLE_DEVICES=all
ENV NVIDIA_DRIVER_CAPABILITIES=compute,utility

ARG VERSION
LABEL version="$VERSION"
LABEL maintainer="<EMAIL>"
COPY --from=nvbuild /libvgpu/build/libvgpu.so /k8s-vgpu/lib/nvidia/libvgpu.so."$VERSION"
