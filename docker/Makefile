# HAMi License Server Docker Compose Management

.PHONY: help up down restart logs status backup restore clean setup

# Default target
help: ## Show this help message
	@echo "HAMi License Server Docker Compose Management"
	@echo ""
	@echo "Available commands:"
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  %-15s %s\n", $$1, $$2}' $(MAKEFILE_LIST)

setup: ## Initial setup - create directories and copy env file
	@echo "Setting up HAMi License Server environment..."
	@mkdir -p data/postgres backup/postgres keys logs
	@if [ ! -f .env ]; then cp .env.example .env; echo "Created .env file from .env.example"; fi
	@chmod +x scripts/*.sh
	@echo "Setup completed. Please edit .env file with your configuration."

up: ## Start all services
	@echo "Starting HAMi License Server..."
	@docker-compose up -d
	@echo "Services started. Use 'make logs' to view logs."

down: ## Stop all services
	@echo "Stopping HAMi License Server..."
	@docker-compose down

restart: ## Restart all services
	@echo "Restarting HAMi License Server..."
	@docker-compose restart

logs: ## View logs from all services
	@docker-compose logs -f

logs-postgres: ## View PostgreSQL logs
	@docker-compose logs -f postgres

logs-license: ## View License Server logs
	@docker-compose logs -f license-server

status: ## Show status of all services
	@docker-compose ps

backup: ## Create database backup
	@echo "Creating database backup..."
	@./scripts/backup-db.sh

restore: ## Restore database from backup (usage: make restore BACKUP=backup_file)
	@if [ -z "$(BACKUP)" ]; then \
		echo "Usage: make restore BACKUP=backup_file"; \
		echo "Example: make restore BACKUP=backup/postgres/backup_20241226_120000.sql.gz"; \
		exit 1; \
	fi
	@./scripts/restore-db.sh $(BACKUP)

clean: ## Remove all containers, volumes, and networks
	@echo "Warning: This will remove all data!"
	@read -p "Are you sure? [y/N] " -n 1 -r; \
	echo; \
	if [[ $$REPLY =~ ^[Yy]$$ ]]; then \
		docker-compose down -v --remove-orphans; \
		docker system prune -f; \
		echo "Cleanup completed."; \
	else \
		echo "Cleanup cancelled."; \
	fi

health: ## Check health of all services
	@echo "Checking service health..."
	@docker-compose exec postgres pg_isready -U postgres || echo "PostgreSQL: Not ready"
	@curl -f http://localhost:8000/health 2>/dev/null && echo "License Server: Healthy" || echo "License Server: Not healthy"

shell-postgres: ## Open PostgreSQL shell
	@docker-compose exec postgres psql -U postgres -d dynamia

shell-license: ## Open License Server container shell
	@docker-compose exec license-server /bin/sh
