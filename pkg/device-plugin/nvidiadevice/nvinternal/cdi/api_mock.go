// Code generated by moq; DO NOT EDIT.
// github.com/matryer/moq

package cdi

import (
	"sync"
)

// Ensure, that InterfaceMock does implement Interface.
// If this is not the case, regenerate this file with moq.
var _ Interface = &InterfaceMock{}

// InterfaceMock is a mock implementation of Interface.
//
//	func TestSomethingThatUsesInterface(t *testing.T) {
//
//		// make and configure a mocked Interface
//		mockedInterface := &InterfaceMock{
//			CreateSpecFileFunc: func() error {
//				panic("mock out the CreateSpecFile method")
//			},
//			QualifiedNameFunc: func(s1 string, s2 string) string {
//				panic("mock out the QualifiedName method")
//			},
//		}
//
//		// use mockedInterface in code that requires Interface
//		// and then make assertions.
//
//	}
type InterfaceMock struct {
	// CreateSpecFileFunc mocks the CreateSpecFile method.
	CreateSpecFileFunc func() error

	// QualifiedNameFunc mocks the QualifiedName method.
	QualifiedNameFunc func(s1 string, s2 string) string

	// calls tracks calls to the methods.
	calls struct {
		// CreateSpecFile holds details about calls to the CreateSpecFile method.
		CreateSpecFile []struct {
		}
		// QualifiedName holds details about calls to the QualifiedName method.
		QualifiedName []struct {
			// S1 is the s1 argument value.
			S1 string
			// S2 is the s2 argument value.
			S2 string
		}
	}
	lockCreateSpecFile sync.RWMutex
	lockQualifiedName  sync.RWMutex
}

// CreateSpecFile calls CreateSpecFileFunc.
func (mock *InterfaceMock) CreateSpecFile() error {
	callInfo := struct {
	}{}
	mock.lockCreateSpecFile.Lock()
	mock.calls.CreateSpecFile = append(mock.calls.CreateSpecFile, callInfo)
	mock.lockCreateSpecFile.Unlock()
	if mock.CreateSpecFileFunc == nil {
		var (
			errOut error
		)
		return errOut
	}
	return mock.CreateSpecFileFunc()
}

// CreateSpecFileCalls gets all the calls that were made to CreateSpecFile.
// Check the length with:
//
//	len(mockedInterface.CreateSpecFileCalls())
func (mock *InterfaceMock) CreateSpecFileCalls() []struct {
} {
	var calls []struct {
	}
	mock.lockCreateSpecFile.RLock()
	calls = mock.calls.CreateSpecFile
	mock.lockCreateSpecFile.RUnlock()
	return calls
}

// QualifiedName calls QualifiedNameFunc.
func (mock *InterfaceMock) QualifiedName(s1 string, s2 string) string {
	callInfo := struct {
		S1 string
		S2 string
	}{
		S1: s1,
		S2: s2,
	}
	mock.lockQualifiedName.Lock()
	mock.calls.QualifiedName = append(mock.calls.QualifiedName, callInfo)
	mock.lockQualifiedName.Unlock()
	if mock.QualifiedNameFunc == nil {
		var (
			sOut string
		)
		return sOut
	}
	return mock.QualifiedNameFunc(s1, s2)
}

// QualifiedNameCalls gets all the calls that were made to QualifiedName.
// Check the length with:
//
//	len(mockedInterface.QualifiedNameCalls())
func (mock *InterfaceMock) QualifiedNameCalls() []struct {
	S1 string
	S2 string
} {
	var calls []struct {
		S1 string
		S2 string
	}
	mock.lockQualifiedName.RLock()
	calls = mock.calls.QualifiedName
	mock.lockQualifiedName.RUnlock()
	return calls
}
