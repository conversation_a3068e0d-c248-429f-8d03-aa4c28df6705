# testdata/invalid_kubeconfig.yaml
apiVersion: v1
kind: Config
clusters:
  - cluster:
    # Missing server field or invalid URL
    # server: http://invalid-url/
    name: broken-cluster
    insecure-skip-tls-verify: true
users:
  - name: broken-user
    user:
      # Invalid or missing token
      token: not-a-valid-token
contexts:
  - context:
      cluster: non-existent-cluster
      user: non-existent-user
    name: broken-context
# Missing current-context