/*
Copyright 2025 The HAMi Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package storage

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"time"

	"github.com/go-sql-driver/mysql"
	"github.com/jackc/pgx/v5/stdlib"
	"gorm.io/datatypes"
	gmysql "gorm.io/driver/mysql"
	gpostgres "gorm.io/driver/postgres"
	"gorm.io/gorm"

	"github.com/Project-HAMi/HAMi/pkg/license"
)

type License struct {
	ID uint `gorm:"primaryKey"`

	LicenseID        string         `gorm:"not null,uniqueIndex:uni_license_id"`
	ExpireTime       time.Time      `gorm:"not null"`
	CreateTime       time.Time      `gorm:"not null"`
	ReminderDuration int            `gorm:""`
	Customer         string         `gorm:""`
	Partner          string         `gorm:""`
	Phase            string         `gorm:""`
	Type             int            `gorm:"not null"`
	ESN              string         `gorm:"not null"`
	DeviceIds        datatypes.JSON `gorm:""`
	DeviceModels     datatypes.JSON `gorm:""`
	Comment          string         `gorm:""`
	Digest           string         `gorm:""`
	Cipher           string         `gorm:"not null"`
}

type Storage struct {
	db *gorm.DB
}

func NewStorage(storageType, dns string) (*Storage, error) {
	if dns == "" {
		return nil, fmt.Errorf("storage dns should not be empty")
	}

	// Only support for mysql and postgres.
	cfg := &Config{DSN: dns, Type: storageType}

	var dialector gorm.Dialector
	switch cfg.Type {
	case "mysql":
		mysqlConfig, err := cfg.genMySQLConfig()
		if err != nil {
			return nil, err
		}
		connector, err := mysql.NewConnector(mysqlConfig)
		if err != nil {
			return nil, err
		}

		cfg.addMysqlErrorNumbers()
		dialector = gmysql.New(gmysql.Config{Conn: sql.OpenDB(connector)})
	case "postgres":
		pgconfig, err := cfg.genPostgresConfig()
		if err != nil {
			return nil, err
		}

		dialector = gpostgres.New(gpostgres.Config{Conn: stdlib.OpenDB(*pgconfig)})
	default:
		return nil, fmt.Errorf("unsupported storage type: %s", cfg.Type)
	}

	db, err := gorm.Open(dialector, &gorm.Config{SkipDefaultTransaction: true})
	if err != nil {
		return nil, err
	}

	sqlDB, err := db.DB()
	if err != nil {
		return nil, err
	}
	connPool, err := cfg.getConnPoolConfig()
	if err != nil {
		return nil, err
	}
	sqlDB.SetMaxIdleConns(connPool.MaxIdleConns)
	sqlDB.SetMaxOpenConns(connPool.MaxOpenConns)
	sqlDB.SetConnMaxLifetime(connPool.ConnMaxLifetime)

	if err := db.AutoMigrate(&License{}); err != nil {
		return nil, err
	}

	return &Storage{db}, nil
}

func (s *Storage) CreateLicense(license *License) error {
	return s.db.Create(license).Error
}

func (s *Storage) ListLicenses(ctx context.Context) ([]License, error) {
	var licenses []License
	if err := s.db.Find(&licenses).Error; err != nil {
		return nil, err
	}
	return licenses, nil
}

// ConvertLicenseToStorage converts a license.License to storage.License.
func ConvertLicenseToStorage(src *license.License, cipher string) (*License, error) {
	if src == nil {
		return nil, fmt.Errorf("source license cannot be nil")
	}

	// Convert DeviceIds []string to datatypes.JSON
	var deviceIdsJSON datatypes.JSON
	if len(src.DeviceIds) > 0 {
		deviceIdsBytes, err := json.Marshal(src.DeviceIds)
		if err != nil {
			return nil, fmt.Errorf("failed to marshal DeviceIds to JSON: %w", err)
		}
		deviceIdsJSON = datatypes.JSON(deviceIdsBytes)
	}

	// Convert DeviceModels []*Model to datatypes.JSON
	var deviceModelsJSON datatypes.JSON
	if src.DeviceModels != nil {
		deviceModelsBytes, err := json.Marshal(src.DeviceModels)
		if err != nil {
			return nil, fmt.Errorf("failed to marshal DeviceModels to JSON: %w", err)
		}
		deviceModelsJSON = datatypes.JSON(deviceModelsBytes)
	}

	return &License{
		LicenseID:        src.Id,
		ExpireTime:       src.ExpireTime,
		CreateTime:       src.CreateTime,
		ReminderDuration: src.ReminderDuration,
		Customer:         src.Customer,
		Partner:          src.Partner,
		Phase:            src.Phase,
		Type:             int(src.Type),
		ESN:              src.ESN,
		DeviceIds:        deviceIdsJSON,
		DeviceModels:     deviceModelsJSON,
		Comment:          src.Comment,
		Digest:           src.Digest,
		Cipher:           cipher,
	}, nil
}

// ConvertStorageToLicense converts a storage.License to license.License.
func ConvertStorageToLicense(src *License) (*license.License, error) {
	if src == nil {
		return nil, fmt.Errorf("source storage license cannot be nil")
	}

	// Convert DeviceIds datatypes.JSON to []string.
	var deviceIds []string
	if len(src.DeviceIds) > 0 {
		if err := json.Unmarshal(src.DeviceIds, &deviceIds); err != nil {
			return nil, fmt.Errorf("failed to unmarshal DeviceIds from JSON: %w", err)
		}
	}

	// Convert DeviceModels datatypes.JSON to []*Model.
	var deviceModels []*license.Model
	if len(src.DeviceModels) > 0 {
		if err := json.Unmarshal(src.DeviceModels, &deviceModels); err != nil {
			return nil, fmt.Errorf("failed to unmarshal DeviceModels from JSON: %w", err)
		}
	}

	return &license.License{
		Id:               src.LicenseID,
		ExpireTime:       src.ExpireTime,
		CreateTime:       src.CreateTime,
		ReminderDuration: src.ReminderDuration,
		Customer:         src.Customer,
		Partner:          src.Partner,
		Phase:            src.Phase,
		Type:             license.LicenseType(src.Type),
		ESN:              src.ESN,
		DeviceIds:        deviceIds,
		DeviceModels:     deviceModels,
		Comment:          src.Comment,
		Digest:           src.Digest,
		Cipher:           src.Cipher,
	}, nil
}
