/*
Copyright 2025 The HAMi Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package license

import (
	"context"
	"errors"
	"os"
	"slices"
	"strings"
	"time"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/klog/v2"

	"github.com/Project-HAMi/HAMi/pkg/util"
	"github.com/Project-HAMi/HAMi/pkg/util/client"
)

const (
	// LicenseSecretLabelKey is the label key for hami license secret.
	LicenseSecretLabelKey = "hami.io/license"

	// LicenseDecryptKeyLabel<PERSON>ey is the label key for hami license decrypt key secret.
	LicenseDecryptKeyLabelKey = "hami.io/license-decrypt-key"
)

var (
	// ErrCurrentNamespaceNotFound is returned when the namespace cannot be found in the default locations.
	ErrCurrentNamespaceNotFound = errors.New("current namespace not found")
)

type LicenseUsage struct {
	LicenseT License
	Used     int
}
type LicenseManager struct {
	Licenses []*LicenseUsage
}

func InitLicenseManager() LicenseManager {
	l, err := ListLicenses(context.Background())
	if err != nil {
		klog.Errorf("ListLicenses failed: %s", err.Error())
		return LicenseManager{}
	}
	usage := LicenseManager{}
	for _, val := range l {
		usage.Licenses = append(usage.Licenses, &LicenseUsage{
			LicenseT: *val,
			Used:     0,
		})
	}
	klog.V(3).InfoS("License initialized:", "licenses", usage)
	return usage
}

func inside(ar []string, key string) bool {
	return slices.Contains(ar, key)
}

func fit(l LicenseUsage, uuid, cardtype string) (bool, bool) {
	if inside(l.LicenseT.DeviceIds, uuid) {
		if len(l.LicenseT.DeviceIds) > l.Used {
			return true, true
		}
		return true, false
	}
	if l.LicenseT.DeviceModels == nil {
		return false, false
	}

	for _, val := range l.LicenseT.DeviceModels {
		if val.DeviceType == cardtype {
			if val.Count > l.Used {
				return true, true
			}
			return true, false
		}
	}

	return false, false
}

func (l *LicenseManager) Replace(uuidl, cardtypel, uuidr, cardtyper string) (bool, time.Time) {
	t := time.Time{}
	found := false
	for _, val := range l.Licenses {
		f, ok := fit(*val, uuidr, cardtyper)
		if f {
			found = true
		}
		if f && !ok {
			found1, _ := fit(*val, uuidl, cardtypel)
			if !found1 {
				return false, time.Time{}
			}
		}
	}
	if !found {
		return false, time.Time{}
	}
	l.Unassign(uuidl, cardtypel)
	_, tmp := l.Assign(uuidr, cardtyper)
	klog.Infoln("tmp=", tmp)
	if t.IsZero() || t.Before(tmp) {
		t = tmp
	}
	return true, t
}

func (l *LicenseManager) Assign(uuid, cardtype string) (bool, time.Time) {
	t := time.Time{}
	for _, val := range l.Licenses {
		found, ok := fit(*val, uuid, cardtype)
		if !found {
			continue
		}
		if !ok {
			return false, time.Time{}
		}
	}
	for _, val := range l.Licenses {
		_, ok := fit(*val, uuid, cardtype)
		if ok {
			val.Used++
			if t.IsZero() || t.Before(val.LicenseT.ExpireTime) {
				t = val.LicenseT.ExpireTime
			}
		}
	}
	if !t.IsZero() {
		return true, t
	}
	return false, t
}

func (l *LicenseManager) Unassign(uuid, cardType string) bool {
	for _, val := range l.Licenses {
		found, _ := fit(*val, uuid, cardType)
		if found {
			if val.Used > 0 {
				val.Used--
			}
		}
	}
	return false
}

func ListLicenses(ctx context.Context) ([]*License, error) {
	// list all hami license secret.
	namespace := GetCurrentNamespaceOrDefault()
	labelSelector := metav1.ListOptions{LabelSelector: LicenseSecretLabelKey}
	if client.GetClient() == nil {
		return nil, errors.New("get client failed")
	}
	secrets, err := client.GetClient().CoreV1().Secrets(namespace).List(ctx, labelSelector)
	if err != nil {
		klog.ErrorS(err, "failed to list secrets for license")
		return nil, err
	}

	// get license decrypt key from secret.
	var decryptKeyData []byte
	labelSelectorForKey := metav1.ListOptions{LabelSelector: LicenseDecryptKeyLabelKey}
	decryptKeys, err := client.GetClient().CoreV1().Secrets(namespace).List(ctx, labelSelectorForKey)
	if err != nil {
		klog.ErrorS(err, "failed to list decrypt key for license")
		return nil, err
	}
	if len(decryptKeys.Items) == 0 {
		err := errors.New("no license decrypt key secret found")
		klog.ErrorS(err, "failed to find decrypt key for license")
		return nil, err
	}
	if len(decryptKeys.Items) > 1 {
		klog.InfoS("multiple license decrypt key secrets found, using the first one", "count", len(decryptKeys.Items))
	}
	for _, secret := range decryptKeys.Items {
		if data, ok := secret.Data["private.key"]; ok {
			decryptKeyData = data
			break
		}
	}
	if decryptKeyData == nil {
		klog.ErrorS(err, "failed to find decrypt key for license")
	}

	manager, err := NewDecodeOnlyManagerFor(decryptKeyData)
	if err != nil {
		klog.ErrorS(err, "failed to create license manager")
		return nil, err
	}

	licenses := []*License{}
	for _, secret := range secrets.Items {
		licenseBytes, ok := secret.Data["license"]
		if !ok {
			klog.ErrorS(nil, "failed to find license in secret", "secret", secret.Name)
			continue
		}
		license, err := manager.Decode(licenseBytes)
		if err != nil {
			klog.ErrorS(err, "failed to decode license", "secret", secret.Name)
			continue
		}
		licenses = append(licenses, license)
	}
	return licenses, nil
}

func GetCurrentNamespaceOrDefault() string {
	ns, err := GetCurrentNamespace()
	if err != nil {
		return "hami-system"
	}
	return ns
}

// GetCurrentNamespace fetch namespace the current pod running in.
func GetCurrentNamespace() (string, error) {
	if ns := os.Getenv("POD_NAMESPACE"); ns != "" {
		return ns, nil
	}

	if data, err := os.ReadFile("/var/run/secrets/kubernetes.io/serviceaccount/namespace"); err == nil {
		if ns := strings.TrimSpace(string(data)); len(ns) > 0 {
			return ns, nil
		}
	}
	return "", ErrCurrentNamespaceNotFound
}

func CheckLicense(dev *util.DeviceUsage) bool {
	if dev.Validate {
		return time.Now().Before(dev.LicenseExpire)
	}
	return true
}

func (l *LicenseManager) AssignDeviceLicenses(devices []*util.DeviceInfo) {
	for _, val := range devices {
		val.Validate = true
		ok, t := l.Assign(val.ID, val.Type)
		klog.Infoln("ok=", ok, "t=", t)
		if ok {
			val.LicenseExpire = t
		}
	}
}
