/*
Copyright 2025 The HAMi Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package license

import (
	"crypto/aes"
	"crypto/rand"
	"crypto/rsa"
	"crypto/sha256"
	"crypto/x509"
	"encoding/base64"
	"encoding/json"
	"encoding/pem"
	"errors"
	"fmt"
	"hash"
	"io"
	"os"
	"time"

	"k8s.io/klog/v2"
)

type KeyConfig struct {
	PrivateKey rsa.PrivateKey
	PubicKey   rsa.PublicKey
}

type CardLicense struct {
	Uuid    string `json:"Uuid"`
	Expired string `json:"Expired"`
}

type NodeLicenseconfigType struct {
	Cards map[string]string `json:"cards"`
}
type LicenseConfig struct {
	NodeLicenseconfig map[string]NodeLicenseconfigType `json:"nodelicenseconfig"`
	Timestamp         string                           `json:"timestamp"`
	Activate          bool                             `json:"activate"`
}

var AESKey = []byte{
	0x00, 0x01, 0x02, 0x03,
	0x04, 0x05, 0x06, 0x07,
	0x08, 0x09, 0x0a, 0x0b,
	0x0c, 0x0d, 0x0e, 0x0f,
	0x10, 0x11, 0x12, 0x13,
	0x14, 0x15, 0x16, 0x17,
	0x18, 0x19, 0x1a, 0x1b,
	0x1c, 0x1d, 0x1e, 0x1f}

func GenRsaKeyPairs() (KeyConfig, error) {
	privateKey, err := rsa.GenerateKey(rand.Reader, 1024)
	if err != nil {
		return KeyConfig{}, err
	}
	return KeyConfig{
		PrivateKey: *privateKey,
		PubicKey:   privateKey.PublicKey,
	}, nil
}

func OutputRsaKey(k KeyConfig) (prvkey, pubkey []byte) {
	derStream := x509.MarshalPKCS1PrivateKey(&k.PrivateKey)
	block := &pem.Block{
		Type:  "RSA PRIVATE KEY",
		Bytes: derStream,
	}
	prvkey = pem.EncodeToMemory(block)
	publicKey := &k.PrivateKey.PublicKey
	derPkix, err := x509.MarshalPKIXPublicKey(publicKey)
	if err != nil {
		panic(err)
	}
	block = &pem.Block{
		Type:  "PUBLIC KEY",
		Bytes: derPkix,
	}
	pubkey = pem.EncodeToMemory(block)
	return
}

func ImportPrivateKey(privKey []byte) (rsa.PrivateKey, error) {
	block, _ := pem.Decode(privKey)
	if block == nil {
		return rsa.PrivateKey{}, errors.New("private key error")
	}
	priv, err := x509.ParsePKCS1PrivateKey(block.Bytes)
	return *priv, err
}

func ImportPubKey(pubkey []byte) (rsa.PublicKey, error) {
	block, _ := pem.Decode(pubkey)
	if block == nil {
		return rsa.PublicKey{}, errors.New("public key error")
	}
	pub, err := x509.ParsePKIXPublicKey(block.Bytes)
	if err != nil {
		return rsa.PublicKey{}, err
	}
	pubk, ok := pub.(*rsa.PublicKey)
	if !ok {
		return rsa.PublicKey{}, errors.New("failed to convert to *rsa.PublicKey")
	}
	return *pubk, nil
}

func EncryptOAEP(hash hash.Hash, random io.Reader, public *rsa.PublicKey, msg []byte, label []byte) ([]byte, error) {
	msgLen := len(msg)
	step := public.Size() - 2*hash.Size() - 2
	var encryptedBytes []byte

	for start := 0; start < msgLen; start += step {
		finish := start + step
		if finish > msgLen {
			finish = msgLen
		}

		encryptedBlockBytes, err := rsa.EncryptOAEP(hash, random, public, msg[start:finish], label)
		if err != nil {
			return nil, err
		}

		encryptedBytes = append(encryptedBytes, encryptedBlockBytes...)
	}

	return encryptedBytes, nil
}

func DecryptOAEP(hash hash.Hash, random io.Reader, private *rsa.PrivateKey, msg []byte, label []byte) ([]byte, error) {
	msgLen := len(msg)
	step := private.Size()
	var decryptedBytes []byte

	for start := 0; start < msgLen; start += step {
		finish := start + step
		if finish > msgLen {
			finish = msgLen
		}

		decryptedBlockBytes, err := rsa.DecryptOAEP(hash, random, private, msg[start:finish], label)
		if err != nil {
			return nil, err
		}

		decryptedBytes = append(decryptedBytes, decryptedBlockBytes...)
	}

	return decryptedBytes, nil
}

func RSA_OAEP_Encrypt(secretMessage string, key rsa.PublicKey) (string, error) {
	label := []byte("OAEP Encrypted")
	rng := rand.Reader
	ciphertext, err := EncryptOAEP(sha256.New(), rng, &key, []byte(secretMessage), label)
	return base64.StdEncoding.EncodeToString(ciphertext), err
}

func RSA_OAEP_Decrypt(cipherText string, privKey rsa.PrivateKey) (string, error) {
	ct, _ := base64.StdEncoding.DecodeString(cipherText)
	label := []byte("OAEP Encrypted")
	rng := rand.Reader
	plaintext, err := DecryptOAEP(sha256.New(), rng, &privKey, ct, label)
	fmt.Println("Plaintext:", string(plaintext))
	return string(plaintext), err
}

func ReadFromLicense(license string) (LicenseConfig, error) {
	var jsonbyte []byte
	var err error
	if len(license) == 0 {
		jsonbyte, err = os.ReadFile("/license/license")
		if err != nil {
			return LicenseConfig{}, err
		}
	} else {
		jsonbyte = []byte(license)
	}
	var licenseconfigs LicenseConfig
	err = json.Unmarshal(jsonbyte, &licenseconfigs)
	if err != nil {
		return LicenseConfig{}, err
	}
	klog.Infof("License Configs: %v", fmt.Sprintf("%v", licenseconfigs))
	return licenseconfigs, nil
}

func ValidateDeviceLicense(c LicenseConfig, node string, devUUID string) bool {
	timeString, ok := c.NodeLicenseconfig[node].Cards[devUUID]
	if !ok {
		timeString, ok = c.NodeLicenseconfig[node].Cards["all"]
	}
	if !ok {
		return false
	}
	theTime, err := time.Parse("2006-01-02", timeString)
	if err != nil {
		fmt.Println("Could not parse time:", err)
	}
	if time.Now().After(theTime) {
		return false
	}
	return true
}

func GenerateLicense(c LicenseConfig) (string, error) {
	b, err := json.Marshal(c)
	return string(b), err
}

func AES_Encrypt(msg string, filename string) error {
	cipher, err := aes.NewCipher(AESKey)
	if err != nil {
		return err
	}
	result := []byte{}
	dst := make([]byte, 16)
	pos := 0
	for pos <= len(msg) {
		substr := []byte(msg[pos:])
		for len(substr) < 16 {
			substr = append(substr, 0)
		}
		cipher.Encrypt(dst, substr)
		klog.V(3).InfoS("Encrypting", "dst", string(dst))
		result = append(result, dst...)
		pos += 16
	}
	err = os.WriteFile(filename, result, os.ModePerm)
	if err != nil {
		return err
	}
	return nil
}
