/*
Copyright 2025 The HAMi Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package license

import (
	"bytes"
	"crypto/rand"
	"crypto/rsa"
	"crypto/sha512"
	"crypto/x509"
	"encoding/base64"
	"encoding/hex"
	"encoding/json"
	"encoding/pem"
	"fmt"
	"os"
)

const (
	RSAPrivateKey = `
************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************`

	RSAPublicKey = `
-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAwGHeJakmMmYD5RtuMAID
hbVDepBqFH18DmHdxuKMzHYTwx+gc8nqgcZS7Dbr5tjtsdB9UoYSHMZxUHsLKLxS
NJG3DtMzmvPE+aU/uGXECBQbR+EPaDHNgaPxjspMeWWs9P61nbFpW9EAlclbcqfD
TbPlteeG1RwHNPtsbf6uBGeHSWEPSxQO9rbehuESqz6WLOt4DAPa8jaZvYB/xBrB
XgLzVMgydhjniWb9hPrTOiEM+AqwLfrPA1Kv33Mg7ErzfrgHvJd9TPmYUJZFJtGO
qhr7hAoO/CQmO0gDT748biNub4zyEhxMuUeHj2mSyhkIeKFBECyXOQXEDihakFCI
iwIDAQAB
-----END PUBLIC KEY-----
`

	// RSAKeySize defines the minimum acceptable RSA key size in bits.
	RSAKeySize = 2048
)

// Manager provides both encoding and decoding capabilities for licenses.
type Manager struct {
	EncodeOnly
	DecodeOnly
}

// NewLicenseManager creates a new license manager with both encoding and
// decoding capabilities.
func NewLicenseManager(encryptKeyPath, decryptKeyPath string) (*Manager, error) {
	encodeOnly, err := NewEncodeOnlyManager(encryptKeyPath)
	if err != nil {
		return nil, fmt.Errorf("failed to create encode-only manager: %w", err)
	}

	decodeOnly, err := NewDecodeOnlyManager(decryptKeyPath)
	if err != nil {
		return nil, fmt.Errorf("failed to create decode-only manager: %w", err)
	}

	return &Manager{
		EncodeOnly: encodeOnly,
		DecodeOnly: decodeOnly,
	}, nil
}

// EncodeOnly defines the interface for license encoding operations.
type EncodeOnly interface {
	Encode(license *License) ([]byte, error)
}

// EncodeOnlyManager handles license encoding using RSA public key encryption.
type EncodeOnlyManager struct {
	encryptKey *rsa.PublicKey
}

// NewEncodeOnlyManager creates a new encode-only manager.
func NewEncodeOnlyManager(encryptKeyPath string) (*EncodeOnlyManager, error) {
	var encryptKeyData []byte

	// Use external key file if provided, otherwise use default key.
	if encryptKeyPath != "" {
		var err error
		encryptKeyData, err = os.ReadFile(encryptKeyPath)
		if err != nil {
			return nil, fmt.Errorf("failed to read encrypt key file %s: %w", encryptKeyPath, err)
		}
	} else {
		encryptKeyData = []byte(RSAPublicKey)
	}

	publicKey, err := PemToPublicKey(encryptKeyData)
	if err != nil {
		return nil, fmt.Errorf("failed to parse public key: %w", err)
	}

	// Validate key size for security.
	if publicKey.N.BitLen() < RSAKeySize {
		return nil, fmt.Errorf("RSA key size %d is below minimum required size %d", publicKey.N.BitLen(), RSAKeySize)
	}

	return &EncodeOnlyManager{encryptKey: publicKey}, nil
}

// Encode encrypts and encodes a license into a base64-encoded ciphertext.
func (e *EncodeOnlyManager) Encode(license *License) ([]byte, error) {
	if license == nil {
		return nil, fmt.Errorf("license cannot be nil")
	}

	licenseData, err := json.Marshal(license)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal license to JSON: %w", err)
	}

	// Implement digest verification for enhanced security
	// This would involve generating a hash of the license data
	// and storing it in the license.Digest field for integrity verification.
	license.Digest = SHA512Digest(string(licenseData))

	// json marshal again.
	licenseData, err = json.Marshal(license)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal license to JSON: %w", err)
	}

	encryptedData, err := e.Encrypt(licenseData)
	if err != nil {
		return nil, fmt.Errorf("failed to encrypt license data: %w", err)
	}

	// Encode to base64 for safe transport.
	encodedLen := base64.RawStdEncoding.EncodedLen(len(encryptedData))
	result := make([]byte, encodedLen)
	base64.RawStdEncoding.Encode(result, encryptedData)

	return result, nil
}

// Encrypt encrypts plaintext using RSA PKCS1v15 with chunking for large data.
func (e *EncodeOnlyManager) Encrypt(plaintext []byte) ([]byte, error) {
	if e.encryptKey == nil {
		return nil, fmt.Errorf("encryption key is not initialized")
	}

	if len(plaintext) == 0 {
		return nil, fmt.Errorf("plaintext cannot be empty")
	}

	maxChunkSize := e.encryptKey.N.BitLen()/8 - 11
	if maxChunkSize <= 0 {
		return nil, fmt.Errorf("RSA key size too small for encryption")
	}

	chunks := split(plaintext, maxChunkSize)
	var encryptedBuffer bytes.Buffer

	for i, chunk := range chunks {
		encryptedChunk, err := rsa.EncryptPKCS1v15(rand.Reader, e.encryptKey, chunk)
		if err != nil {
			return nil, fmt.Errorf("failed to encrypt chunk %d: %w", i, err)
		}
		encryptedBuffer.Write(encryptedChunk)
	}

	return encryptedBuffer.Bytes(), nil
}

// DecodeOnly defines the interface for license decoding operations.
type DecodeOnly interface {
	Decode(ciphertext []byte) (*License, error)
}

// DecodeOnlyManager handles license decoding using RSA private key decryption.
type DecodeOnlyManager struct {
	decryptKey *rsa.PrivateKey
}

// NewDecodeOnlyManager creates a new decode-only manager.
func NewDecodeOnlyManager(decryptKeyPath string) (*DecodeOnlyManager, error) {
	var decryptKeyData []byte

	// Use external key file if provided, otherwise use default key.
	if decryptKeyPath != "" {
		var err error
		decryptKeyData, err = os.ReadFile(decryptKeyPath)
		if err != nil {
			return nil, fmt.Errorf("failed to read decrypt key file %s: %w", decryptKeyPath, err)
		}
	} else {
		decryptKeyData = []byte(RSAPrivateKey)
	}

	return NewDecodeOnlyManagerFor(decryptKeyData)
}

// NewDecodeOnlyManagerFor creates a new decode-only manager from key data.
func NewDecodeOnlyManagerFor(decryptKeyData []byte) (*DecodeOnlyManager, error) {
	if len(decryptKeyData) == 0 {
		decryptKeyData = []byte(RSAPrivateKey)
	}

	privateKey, err := PemToPrivateKey(decryptKeyData)
	if err != nil {
		return nil, fmt.Errorf("failed to parse private key: %w", err)
	}

	// Validate key size for security.
	if privateKey.N.BitLen() < RSAKeySize {
		return nil, fmt.Errorf("RSA key size %d is below minimum required size %d", privateKey.N.BitLen(), RSAKeySize)
	}

	return &DecodeOnlyManager{decryptKey: privateKey}, nil
}

// Decode decodes and decrypts a base64-encoded license ciphertext.
func (d *DecodeOnlyManager) Decode(ciphertext []byte) (*License, error) {
	if len(ciphertext) == 0 {
		return nil, fmt.Errorf("ciphertext cannot be empty")
	}

	decodedLen := base64.RawStdEncoding.DecodedLen(len(ciphertext))
	encryptedData := make([]byte, decodedLen)
	actualLen, err := base64.RawStdEncoding.Decode(encryptedData, ciphertext)
	if err != nil {
		return nil, fmt.Errorf("failed to decode base64 ciphertext: %w", err)
	}

	encryptedData = encryptedData[:actualLen]

	// Decrypt the license data.
	licenseData, err := d.Decrypt(encryptedData)
	if err != nil {
		return nil, fmt.Errorf("failed to decrypt license data: %w", err)
	}

	license := &License{}
	if err := json.Unmarshal(licenseData, license); err != nil {
		return nil, fmt.Errorf("failed to unmarshal license JSON: %w", err)
	}

	// Implement digest verification for enhanced security
	// This would verify the integrity of the license data using
	// a hash stored in the license.Digest field.
	digest := license.Digest

	// verify license digest.
	license.Digest = ""
	licenseData, err = json.Marshal(license)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal license to JSON: %w", err)
	}
	newDigest := SHA512Digest(string(licenseData))
	if digest != newDigest {
		return nil, fmt.Errorf("license digest mismatch")
	}

	license.Digest = digest
	return license, nil
}

// Decrypt decrypts ciphertext using RSA PKCS1v15 with chunking for large data.
func (d *DecodeOnlyManager) Decrypt(ciphertext []byte) ([]byte, error) {
	if d.decryptKey == nil {
		return nil, fmt.Errorf("decryption key is not initialized")
	}

	if len(ciphertext) == 0 {
		return nil, fmt.Errorf("ciphertext cannot be empty")
	}

	chunkSize := d.decryptKey.N.BitLen() / 8
	if chunkSize <= 0 {
		return nil, fmt.Errorf("invalid RSA key size")
	}

	// Validate ciphertext length is multiple of chunk size.
	if len(ciphertext)%chunkSize != 0 {
		return nil, fmt.Errorf("invalid ciphertext length: expected multiple of %d, got %d", chunkSize, len(ciphertext))
	}

	chunks := split(ciphertext, chunkSize)
	var decryptedBuffer bytes.Buffer

	for i, chunk := range chunks {
		decryptedChunk, err := rsa.DecryptPKCS1v15(rand.Reader, d.decryptKey, chunk)
		if err != nil {
			return nil, fmt.Errorf("failed to decrypt chunk %d: %w", i, err)
		}
		decryptedBuffer.Write(decryptedChunk)
	}

	return decryptedBuffer.Bytes(), nil
}

// PemToPrivateKey parses a PEM-encoded RSA private key supporting both PKCS#1 and PKCS#8 formats.
func PemToPrivateKey(pemData []byte) (*rsa.PrivateKey, error) {
	if len(pemData) == 0 {
		return nil, fmt.Errorf("PEM data cannot be empty")
	}

	block, _ := pem.Decode(pemData)
	if block == nil {
		return nil, fmt.Errorf("failed to decode PEM block containing private key")
	}

	var privateKey *rsa.PrivateKey
	var err error

	switch block.Type {
	case "RSA PRIVATE KEY":
		privateKey, err = x509.ParsePKCS1PrivateKey(block.Bytes)
		if err != nil {
			return nil, fmt.Errorf("failed to parse PKCS#1 private key: %w", err)
		}
	case "PRIVATE KEY":
		parsedKey, err := x509.ParsePKCS8PrivateKey(block.Bytes)
		if err != nil {
			return nil, fmt.Errorf("failed to parse PKCS#8 private key: %w", err)
		}

		// Type assertion to ensure it's an RSA private key.
		rsaKey, ok := parsedKey.(*rsa.PrivateKey)
		if !ok {
			return nil, fmt.Errorf("PKCS#8 key is not an RSA private key, got %T", parsedKey)
		}
		privateKey = rsaKey
	default:
		return nil, fmt.Errorf("unsupported PEM block type: %s (expected 'RSA PRIVATE KEY' or 'PRIVATE KEY')", block.Type)
	}

	if err := privateKey.Validate(); err != nil {
		return nil, fmt.Errorf("invalid private key: %w", err)
	}

	return privateKey, nil
}

// PemToPublicKey parses a PEM-encoded RSA public key supporting multiple formats.
func PemToPublicKey(pemData []byte) (*rsa.PublicKey, error) {
	if len(pemData) == 0 {
		return nil, fmt.Errorf("PEM data cannot be empty")
	}

	block, _ := pem.Decode(pemData)
	if block == nil {
		return nil, fmt.Errorf("failed to decode PEM block containing public key")
	}

	var publicKey *rsa.PublicKey
	var err error

	switch block.Type {
	case "RSA PUBLIC KEY":
		publicKey, err = x509.ParsePKCS1PublicKey(block.Bytes)
		if err != nil {
			return nil, fmt.Errorf("failed to parse PKCS#1 public key: %w", err)
		}
	case "PUBLIC KEY":
		publicKeyInterface, err := x509.ParsePKIXPublicKey(block.Bytes)
		if err != nil {
			return nil, fmt.Errorf("failed to parse PKIX public key: %w", err)
		}

		// Type assertion to ensure it's an RSA public key.
		rsaKey, ok := publicKeyInterface.(*rsa.PublicKey)
		if !ok {
			return nil, fmt.Errorf("PKIX key is not an RSA public key, got %T", publicKeyInterface)
		}
		publicKey = rsaKey
	default:
		return nil, fmt.Errorf("unsupported PEM block type: %s (expected 'RSA PUBLIC KEY' or 'PUBLIC KEY')", block.Type)
	}

	return publicKey, nil
}

// split divides a byte slice into chunks of specified maximum size.
func split(data []byte, maxChunkSize int) [][]byte {
	if len(data) == 0 {
		return nil
	}
	if maxChunkSize <= 0 {
		return [][]byte{data}
	}

	// Pre-allocate slice with estimated capacity.
	numChunks := (len(data) + maxChunkSize - 1) / maxChunkSize
	chunks := make([][]byte, 0, numChunks)

	for len(data) > 0 {
		chunkSize := min(len(data), maxChunkSize)

		chunk := data[:chunkSize]
		chunks = append(chunks, chunk)
		data = data[chunkSize:]
	}

	return chunks
}

func SHA512Digest(text string) string {
	hash := sha512.New()
	hash.Write([]byte(text))
	return hex.EncodeToString(hash.Sum(nil))
}
