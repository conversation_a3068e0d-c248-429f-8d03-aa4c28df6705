/*
Copyright 2025 The HAMi Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package license

import "time"

type Model struct {
	DeviceType string `json:"deviceType,omitempty"`
	Count      int    `json:"count,omitempty"`
}

type LicenseType int

var (
	LicenseTypeByID    LicenseType = 1
	LicenseTypeByModel LicenseType = 2
)

type License struct {
	Id               string      `json:"id"`
	ExpireTime       time.Time   `json:"expireTime"`
	CreateTime       time.Time   `json:"createTime"`
	ReminderDuration int         `json:"reminderDuration,omitempty"`
	Customer         string      `json:"customer,omitempty"`
	Partner          string      `json:"partner,omitempty"`
	Phase            string      `json:"phase,omitempty"`
	Type             LicenseType `json:"type,omitempty"`
	ESN              string      `json:"esn,omitempty"`
	DeviceIds        []string    `json:"deviceIds,omitempty"`
	DeviceModels     []*Model    `json:"deviceModels,omitempty"`
	Comment          string      `json:"comment,omitempty"`
	Digest           string      `json:"digest,omitempty"`
	Cipher           string      `json:"cipher,omitempty"`
}
