/*
Copyright 2025 The HAMi Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package routes

import (
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"time"

	"github.com/google/uuid"
	"github.com/julienschmidt/httprouter"
	"k8s.io/klog/v2"

	"github.com/Project-HAMi/HAMi/pkg/license"
	"github.com/Project-HAMi/HAMi/pkg/license/storage"
	"github.com/Project-HAMi/HAMi/pkg/license/totp"
)

const (
	// MaxCommentLength defines the maximum length for license comments.
	MaxCommentLength = 120
	// DefaultErrorCode is the default error code for API responses.
	DefaultErrorCode = 13
)

// EnableGoogleTOTP defines whether to enable Google TOTP for license server.
var EnableGoogleTOTP = true

// GenerateLicenseRequest represents the request payload for generating a license.
type GenerateLicenseRequest struct {
	ExpireTime       time.Time        `json:"expireTime" validate:"required"`
	Customer         string           `json:"customer,omitempty"`
	Partner          string           `json:"partner,omitempty"`
	ReminderDuration int              `json:"reminderDuration,omitempty"`
	Phase            string           `json:"phase,omitempty"`
	Type             int              `json:"type,omitempty" validate:"required,oneof=1 2"`
	ESN              string           `json:"esn,omitempty"`
	DeviceIds        []string         `json:"deviceIds,omitempty"`
	DeviceModels     []*license.Model `json:"deviceModels,omitempty"`
	GoogleTOTPCode   string           `json:"googleTotpCode,omitempty"`
	Comment          string           `json:"comment,omitempty" validate:"max=120"`
}

// GenerateLicenseResponse represents the response payload for license generation.
type GenerateLicenseResponse struct {
	Ciphertext string `json:"ciphertext"`
}

type DecodeLicenseRequest struct {
	GoogleTOTPCode string `json:"googleTotpCode,omitempty" validate:"required"`
	Ciphertext     string `json:"ciphertext" validate:"required"`
}

// ErrResponse represents an error response.
type ErrResponse struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
}

// NewErrResponse creates a new error response with the given message.
func NewErrResponse(message string) string {
	errResponse := &ErrResponse{
		Code:    DefaultErrorCode,
		Message: message,
	}
	response, err := json.Marshal(errResponse)
	if err != nil {
		klog.ErrorS(err, "Failed to marshal error response")
	}
	return string(response)
}

// LicenseRouter handles license-related HTTP routes.
type LicenseRouter struct {
	manager *license.Manager
	storage *storage.Storage
}

// NewLicenseRouter creates a new LicenseRouter instance.
func NewLicenseRouter(encryptKeyPath, decryptKeyPath string, storage *storage.Storage) (*LicenseRouter, error) {
	manager, err := license.NewLicenseManager(encryptKeyPath, decryptKeyPath)
	if err != nil {
		return nil, fmt.Errorf("failed to create license manager: %w", err)
	}

	return &LicenseRouter{manager: manager, storage: storage}, nil
}

// GenerateLicense returns an HTTP handler for generating licenses.
func (route *LicenseRouter) GenerateLicense() httprouter.Handle {
	return func(w http.ResponseWriter, r *http.Request, _ httprouter.Params) {
		defer r.Body.Close()

		var request GenerateLicenseRequest
		if err := json.NewDecoder(r.Body).Decode(&request); err != nil {
			klog.ErrorS(err, "Failed to decode request body")
			http.Error(w, NewErrResponse("Invalid request format: "+err.Error()), http.StatusBadRequest)
			return
		}

		if errs := verifyLicenseRequest(request); len(errs) > 0 {
			klog.ErrorS(errors.Join(errs...), "License request validation failed")
			http.Error(w, NewErrResponse(errors.Join(errs...).Error()), http.StatusBadRequest)
			return
		}

		licenseRow := buildLicenseFromRequest(request)
		ciphertext, err := route.manager.Encode(licenseRow)
		if err != nil {
			klog.ErrorS(err, "Failed to encode license", "licenseID", licenseRow.Id)
			http.Error(w, NewErrResponse("Failed to encode license"), http.StatusInternalServerError)
			return
		}

		// Store license to mysql database.
		license, err := storage.ConvertLicenseToStorage(licenseRow, string(ciphertext))
		if err != nil {
			klog.ErrorS(err, "Failed to convert license to storage format", "licenseID", licenseRow.Id)
			http.Error(w, NewErrResponse(err.Error()), http.StatusInternalServerError)
			return
		}
		if err := route.storage.CreateLicense(license); err != nil {
			klog.ErrorS(err, "Failed to store license", "licenseID", licenseRow.Id)
			http.Error(w, NewErrResponse(err.Error()), http.StatusInternalServerError)
			return
		}

		klog.InfoS("Successfully generated license", "licenseID", licenseRow.Id, "type", licenseRow.Type)

		w.Header().Set("Content-Type", "application/json")
		response := &GenerateLicenseResponse{Ciphertext: string(ciphertext)}
		if err := json.NewEncoder(w).Encode(response); err != nil {
			klog.ErrorS(err, "Failed to encode response")
			http.Error(w, NewErrResponse(err.Error()), http.StatusInternalServerError)
			return
		}
	}
}

// DecodeLicense returns an HTTP handler for decoding licenses.
func (route *LicenseRouter) DecodeLicense() httprouter.Handle {
	return func(w http.ResponseWriter, r *http.Request, _ httprouter.Params) {
		defer r.Body.Close()

		var request DecodeLicenseRequest
		if err := json.NewDecoder(r.Body).Decode(&request); err != nil {
			klog.ErrorS(err, "Failed to decode request body")
			http.Error(w, NewErrResponse("Invalid request format: "+err.Error()), http.StatusBadRequest)
			return
		}

		if errs := verifyDecodeLicenseRequest(request); len(errs) > 0 {
			klog.ErrorS(errors.Join(errs...), "Decode license request validation failed")
			http.Error(w, NewErrResponse(errors.Join(errs...).Error()), http.StatusBadRequest)
			return
		}

		license, err := route.manager.Decode([]byte(request.Ciphertext))
		if err != nil {
			klog.ErrorS(err, "Failed to decode license")
			http.Error(w, NewErrResponse(err.Error()), http.StatusInternalServerError)
			return
		}

		w.Header().Set("Content-Type", "application/json")
		if err := json.NewEncoder(w).Encode(license); err != nil {
			klog.ErrorS(err, "Failed to encode response")
			http.Error(w, NewErrResponse(err.Error()), http.StatusInternalServerError)
			return
		}
	}
}

// ListLicenses returns all licenses.
func (route *LicenseRouter) ListLicenses() httprouter.Handle {
	return func(w http.ResponseWriter, r *http.Request, _ httprouter.Params) {
		// Get GoogleTOTPCode from query parameters
		googleTOTPCode := r.URL.Query().Get("googleTotpCode")

		// Verify Google TOTP if enabled
		if err := verifyGoogleTOTP(googleTOTPCode); err != nil {
			klog.ErrorS(err, "List licenses request validation failed")
			http.Error(w, NewErrResponse(err.Error()), http.StatusBadRequest)
			return
		}

		licenses, err := route.storage.ListLicenses(r.Context())
		if err != nil {
			klog.ErrorS(err, "Failed to list licenses")
			http.Error(w, NewErrResponse(err.Error()), http.StatusInternalServerError)
			return
		}

		resp := []*license.License{}
		for _, lic := range licenses {
			converted, err := storage.ConvertStorageToLicense(&lic)
			if err != nil {
				klog.ErrorS(err, "Failed to convert license to license format", "licenseID", lic.LicenseID)
				http.Error(w, NewErrResponse(err.Error()), http.StatusInternalServerError)
				return
			}
			resp = append(resp, converted)
		}

		w.Header().Set("Content-Type", "application/json")
		if err := json.NewEncoder(w).Encode(resp); err != nil {
			klog.ErrorS(err, "Failed to encode response")
			http.Error(w, NewErrResponse(err.Error()), http.StatusInternalServerError)
			return
		}
	}
}

// verifyLicenseRequest validates the license generation request.
func verifyLicenseRequest(request GenerateLicenseRequest) []error {
	var errs []error

	if request.ExpireTime.IsZero() {
		errs = append(errs, errors.New("license expire time is required"))
	} else if request.ExpireTime.Before(time.Now()) {
		errs = append(errs, errors.New("license expire time must be in the future"))
	}

	if request.Type != int(license.LicenseTypeByID) && request.Type != int(license.LicenseTypeByModel) {
		errs = append(errs, errors.New("license type must be 1 (by ID) or 2 (by model)"))
	}
	if request.Type == int(license.LicenseTypeByID) && len(request.DeviceIds) == 0 {
		errs = append(errs, errors.New("device IDs are required for license type 1"))
	}
	if request.Type == int(license.LicenseTypeByModel) && request.DeviceModels == nil {
		errs = append(errs, errors.New("device models are required for license type 2"))
	}

	if len(request.Comment) > MaxCommentLength {
		errs = append(errs, fmt.Errorf("comment is too long, maximum length is %d characters", MaxCommentLength))
	}
	if err := verifyGoogleTOTP(request.GoogleTOTPCode); err != nil {
		errs = append(errs, err)
	}

	return errs
}

// verifyDecodeLicenseRequest validates the license decoding request.
func verifyDecodeLicenseRequest(request DecodeLicenseRequest) []error {
	var errs []error

	if len(request.Ciphertext) == 0 {
		errs = append(errs, errors.New("license ciphertext is required"))
	}
	if err := verifyGoogleTOTP(request.GoogleTOTPCode); err != nil {
		errs = append(errs, err)
	}

	return errs
}

// verifyGoogleTOTP validates the Google TOTP code if enabled.
func verifyGoogleTOTP(googleTOTPCode string) error {
	if !EnableGoogleTOTP {
		return nil
	}

	if googleTOTPCode == "" {
		return errors.New("google TOTP code is required")
	}
	if !totp.ValidateToTp(googleTOTPCode) {
		return errors.New("invalid Google TOTP code, please try again")
	}

	return nil
}

// buildLicenseFromRequest creates a license object from the request.
func buildLicenseFromRequest(request GenerateLicenseRequest) *license.License {
	return &license.License{
		Id:               uuid.New().String(),
		ExpireTime:       request.ExpireTime,
		CreateTime:       time.Now(),
		ReminderDuration: request.ReminderDuration,
		Customer:         request.Customer,
		Partner:          request.Partner,
		Phase:            request.Phase,
		Type:             license.LicenseType(request.Type),
		ESN:              request.ESN,
		DeviceIds:        request.DeviceIds,
		DeviceModels:     request.DeviceModels,
		Comment:          request.Comment,
	}
}
