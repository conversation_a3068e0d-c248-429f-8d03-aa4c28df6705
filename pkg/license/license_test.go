/*
Copyright 2024 The HAMi Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package license

import (
	"testing"
	"time"

	"gotest.tools/v3/assert"

	"github.com/Project-HAMi/HAMi/pkg/util"
)

func Test_Assign(t *testing.T) {
	tests := []struct {
		name string
		args struct {
			LicenseManager LicenseManager
			uuid           string
			cardtype       string
		}
		t       time.Time
		counted int
		pass    bool
	}{
		{
			name: "assign by uuid",
			args: struct {
				LicenseManager LicenseManager
				uuid           string
				cardtype       string
			}{
				LicenseManager: LicenseManager{
					Licenses: []*LicenseUsage{
						{
							LicenseT: License{
								DeviceIds:  []string{"U1"},
								ExpireTime: time.UnixMilli(1111),
							},
							Used: []string{},
						},
					},
				},
				uuid:     "U1",
				cardtype: "",
			},
			pass:    true,
			t:       time.UnixMilli(1111),
			counted: 1,
		},
		{
			name: "assign by uuid fail",
			args: struct {
				LicenseManager LicenseManager
				uuid           string
				cardtype       string
			}{
				LicenseManager: LicenseManager{
					Licenses: []*LicenseUsage{
						{
							LicenseT: License{
								DeviceIds:  []string{"U1"},
								ExpireTime: time.UnixMilli(1111),
							},
							Used: []string{},
						},
					},
				},
				uuid:     "U2",
				cardtype: "",
			},
			pass:    false,
			t:       time.Time{},
			counted: 0,
		},
		{
			name: "assign by devtype",
			args: struct {
				LicenseManager LicenseManager
				uuid           string
				cardtype       string
			}{
				LicenseManager: LicenseManager{
					Licenses: []*LicenseUsage{
						{
							LicenseT: License{
								DeviceIds: []string{""},
								DeviceModels: []*Model{
									{
										DeviceType: "NVIDIA A10",
										Count:      1,
									},
								},
								ExpireTime: time.UnixMilli(1111),
							},
							Used: []string{},
						},
					},
				},
				uuid:     "U1",
				cardtype: "NVIDIA A10",
			},
			pass:    true,
			t:       time.UnixMilli(1111),
			counted: 1,
		},
		{
			name: "assign by devtype",
			args: struct {
				LicenseManager LicenseManager
				uuid           string
				cardtype       string
			}{
				LicenseManager: LicenseManager{
					Licenses: []*LicenseUsage{
						{
							LicenseT: License{
								DeviceIds: []string{""},
								DeviceModels: []*Model{
									{
										DeviceType: "NVIDIA A10",
										Count:      1,
									},
								},
								ExpireTime: time.UnixMilli(1111),
							},
							Used: []string{},
						},
					},
				},
				uuid:     "U1",
				cardtype: "Cambricon 370",
			},
			pass:    false,
			t:       time.Time{},
			counted: 0,
		},
		{
			name: "assign by cardype",
			args: struct {
				LicenseManager LicenseManager
				uuid           string
				cardtype       string
			}{
				LicenseManager: LicenseManager{
					Licenses: []*LicenseUsage{
						{
							LicenseT: License{
								DeviceIds: []string{""},
								DeviceModels: []*Model{
									{
										DeviceType: "NVIDIA A10",
										Count:      1,
									},
								},
								ExpireTime: time.UnixMilli(1111),
							},
							Used: []string{},
						},
					},
				},
				uuid:     "U1",
				cardtype: "NVIDIA A100",
			},
			pass:    false,
			t:       time.Time{},
			counted: 0,
		},
		{
			name: "assign by cardype",
			args: struct {
				LicenseManager LicenseManager
				uuid           string
				cardtype       string
			}{
				LicenseManager: LicenseManager{
					Licenses: []*LicenseUsage{
						{
							LicenseT: License{
								DeviceIds: []string{""},
								DeviceModels: []*Model{
									{
										DeviceType: "NVIDIA A10",
										Count:      1,
									},
								},
								ExpireTime: time.UnixMilli(1111),
							},
							Used: []string{},
						},
					},
				},
				uuid:     "U1",
				cardtype: "NVIDIA A10",
			},
			pass:    true,
			t:       time.UnixMilli(1111),
			counted: 1,
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			ok, ti := test.args.LicenseManager.Assign(test.args.uuid, test.args.cardtype)
			assert.Equal(t, test.pass, ok)
			assert.Equal(t, test.t, ti)
			assert.Equal(t, len(test.args.LicenseManager.Licenses[0].Used), test.counted)
		})
	}
}

func Test_Replace(t *testing.T) {
	tests := []struct {
		name string
		args struct {
			LicenseManager LicenseManager
			uuidl          string
			cardtypel      string
			uuidr          string
			cardtyper      string
		}
		t       time.Time
		counted int
		pass    bool
	}{
		{
			name: "assign by devicetype",
			args: struct {
				LicenseManager LicenseManager
				uuidl          string
				cardtypel      string
				uuidr          string
				cardtyper      string
			}{
				LicenseManager: LicenseManager{
					Licenses: []*LicenseUsage{
						{
							LicenseT: License{
								DeviceIds: []string{},
								DeviceModels: []*Model{
									{
										DeviceType: "NVIDIA A10",
										Count:      2,
									},
								},
								ExpireTime: time.UnixMilli(1111),
							},
							Used: []string{"U1", "U2"},
						},
					},
				},
				uuidl:     "U1",
				cardtypel: "NVIDIA A10",
				uuidr:     "U3",
				cardtyper: "NVIDIA A100",
			},
			pass:    true,
			t:       time.UnixMilli(1111),
			counted: 2,
		},
		{
			name: "assign by device type fail",
			args: struct {
				LicenseManager LicenseManager
				uuidl          string
				cardtypel      string
				uuidr          string
				cardtyper      string
			}{
				LicenseManager: LicenseManager{
					Licenses: []*LicenseUsage{
						{
							LicenseT: License{
								DeviceIds: []string{},
								DeviceModels: []*Model{
									{
										DeviceType: "NVIDIA A10",
										Count:      1,
									},
									{
										DeviceType: "NVIDIA A100",
										Count:      1,
									},
								},
								ExpireTime: time.UnixMilli(1111),
							},
							Used: []string{"U1", "U2"},
						},
					},
				},
				uuidl:     "U1",
				cardtypel: "NVIDIA A10",
				uuidr:     "U4",
				cardtyper: "Cambricon 370",
			},
			pass:    false,
			t:       time.Time{},
			counted: 2,
		},
		{
			name: "assign by cardtype",
			args: struct {
				LicenseManager LicenseManager
				uuidl          string
				cardtypel      string
				uuidr          string
				cardtyper      string
			}{
				LicenseManager: LicenseManager{
					Licenses: []*LicenseUsage{
						{
							LicenseT: License{
								DeviceIds: []string{},
								DeviceModels: []*Model{
									{
										DeviceType: "NVIDIA A10",
										Count:      1,
									},
									{
										DeviceType: "NVIDIA A100",
										Count:      1,
									},
								},
								ExpireTime: time.UnixMilli(1111),
							},
							Used: []string{"U1", "U2"},
						},
					},
				},
				uuidl:     "U1",
				cardtypel: "NVIDIA A10",
				uuidr:     "U3",
				cardtyper: "NVIDIA A100",
			},
			pass:    true,
			t:       time.UnixMilli(1111),
			counted: 2,
		},
		{
			name: "assign by cardtype fail",
			args: struct {
				LicenseManager LicenseManager
				uuidl          string
				cardtypel      string
				uuidr          string
				cardtyper      string
			}{
				LicenseManager: LicenseManager{
					Licenses: []*LicenseUsage{
						{
							LicenseT: License{
								DeviceIds: []string{},
								DeviceModels: []*Model{
									{
										DeviceType: "NVIDIA A10",
										Count:      1,
									},
									{
										DeviceType: "NVIDIA A100",
										Count:      1,
									},
								},
								ExpireTime: time.UnixMilli(1111),
							},
							Used: []string{"U1", "U2"},
						},
					},
				},
				uuidl:     "U1",
				cardtypel: "NVIDIA A10",
				uuidr:     "U4",
				cardtyper: "Cambricon 370",
			},
			pass:    false,
			t:       time.Time{},
			counted: 2,
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			ok, ti := test.args.LicenseManager.Replace(test.args.uuidl, test.args.cardtypel, test.args.uuidr, test.args.cardtyper)
			assert.Equal(t, test.pass, ok)
			assert.Equal(t, test.t, ti)
			assert.Equal(t, len(test.args.LicenseManager.Licenses[0].Used), test.counted)
		})
	}
}

func TestAssignDeviceLicenses(t *testing.T) {
	tests := []struct {
		name           string
		devices        []*util.DeviceInfo
		licenseUsage   []*LicenseUsage
		expectedValid  []bool
		expectedExpire []time.Time
	}{
		{
			name: "successful license assignment",
			devices: []*util.DeviceInfo{
				{
					ID:   "GPU-123",
					Type: "NVIDIA A10",
				},
				{
					ID:   "GPU-456",
					Type: "NVIDIA A100",
				},
			},
			licenseUsage: []*LicenseUsage{
				{
					LicenseT: License{
						DeviceModels: []*Model{
							{
								DeviceType: "NVIDIA A10",
								Count:      1,
							},
							{
								DeviceType: "NVIDIA A100",
								Count:      1,
							},
						},
						ExpireTime: time.UnixMilli(1111),
					},
					Used: []string{},
				},
			},
			expectedValid:  []bool{true, true},
			expectedExpire: []time.Time{time.UnixMilli(1111), time.UnixMilli(1111)},
		},
		{
			name: "successful license assignment",
			devices: []*util.DeviceInfo{
				{
					ID:   "GPU-123",
					Type: "NVIDIA A10",
				},
				{
					ID:   "GPU-456",
					Type: "NVIDIA A100",
				},
			},
			licenseUsage: []*LicenseUsage{
				{
					LicenseT: License{
						DeviceModels: []*Model{
							{
								DeviceType: "NVIDIA A10",
								Count:      1,
							},
							{
								DeviceType: "NVIDIA A100",
								Count:      1,
							},
						},
						ExpireTime: time.UnixMilli(1111),
					},
					Used: []string{},
				},
			},
			expectedValid:  []bool{true, true},
			expectedExpire: []time.Time{time.UnixMilli(1111), {}},
		},
		{
			name: "failed license assignment",
			devices: []*util.DeviceInfo{
				{
					ID:   "GPU-123",
					Type: "NVIDIA",
				},
			},
			expectedValid:  []bool{true},
			expectedExpire: []time.Time{{}},
		},
		{
			name:    "empty devices list",
			devices: []*util.DeviceInfo{},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ts := LicenseManager{}
			ts.Licenses = tt.licenseUsage
			ts.AssignDeviceLicenses(tt.devices)

			for i, device := range tt.devices {
				assert.Equal(t, tt.expectedValid[i], device.Validate)
				if len(tt.expectedExpire) > i {
					assert.Equal(t, tt.expectedExpire[i], device.LicenseExpire)
				}
			}
		})
	}
}
