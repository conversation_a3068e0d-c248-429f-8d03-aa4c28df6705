/*
Copyright 2025 The HAMi Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package totp

import (
	opt "github.com/pquerna/otp/totp"
)

const (
	GoogleToptSecretKey = "K37CZ6MSUZHPXZK6G6RCRQJHK7PFW6UG"
)

func GenerateToTpSecret() (string, error) {
	key, err := opt.Generate(opt.GenerateOpts{
		Issuer:      "dynamia.ai",
		AccountName: "<EMAIL>",
	})
	if err != nil {
		return "", err
	}

	return key.Secret(), nil
}

func ValidateToTp(code string) bool {
	return opt.Validate(code, GoogleToptSecretKey)
}
