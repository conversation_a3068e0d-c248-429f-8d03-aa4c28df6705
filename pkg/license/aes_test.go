/*
Copyright 2024 The HAMi Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package license

import (
	"fmt"
	"testing"

	"gotest.tools/v3/assert"
)

var inRequestDevices map[string]string

func init() {
	inRequestDevices = make(map[string]string)
	inRequestDevices["NVIDIA"] = "hami.io/vgpu-devices-to-allocate"
}

func TestAESGenerate(t *testing.T) {
	err := AES_Encrypt("GPU-00552014-5c87-89ac-b1a6-7b53aa24b0ec,GPU-0fc3eda5-e98b-a25b-5b0d-cf5c855d1448", "/tmp/li1")
	assert.NilError(t, err)
}

func TestGenerateLicense(t *testing.T) {
	l := LicenseConfig{
		NodeLicenseconfig: map[string]NodeLicenseconfigType{},
		Activate:          false,
		Timestamp:         "BBADSFADSF",
	}
	cc := NodeLicenseconfigType{
		Cards: make(map[string]string),
	}
	cc.Cards["GPU-1"] = "2025-2-12"
	cc.Cards["GPU-2"] = "2025-2-12"
	cc.Cards["GPU-3"] = "2025-2-12"
	l.NodeLicenseconfig["cccc"] = cc
	dd := NodeLicenseconfigType{
		Cards: make(map[string]string),
	}
	dd.Cards["all"] = "2026-2-12"
	l.NodeLicenseconfig["dddd"] = dd
	s, err := GenerateLicense(l)
	fmt.Println("s==", s)
	assert.NilError(t, err)
	decode, err := ReadFromLicense(s)
	assert.NilError(t, err)
	assert.Equal(t, decode.NodeLicenseconfig["cccc"].Cards["GPU-2"], "2025-2-12")
}
