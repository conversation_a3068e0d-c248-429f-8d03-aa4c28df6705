/*
Copyright 2024 The HAMi Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package scheduler

import (
	"fmt"
	"slices"
	"strings"
	"sync"
	"time"

	corev1 "k8s.io/api/core/v1"
	"k8s.io/klog/v2"

	"github.com/Project-HAMi/HAMi/pkg/device"
	"github.com/Project-HAMi/HAMi/pkg/license"
	"github.com/Project-HAMi/HAMi/pkg/scheduler/policy"
	"github.com/Project-HAMi/HAMi/pkg/util"
)

type NodeUsage struct {
	Node    *corev1.Node
	Devices policy.DeviceUsageList
}

type nodeManager struct {
	nodes map[string]*util.NodeInfo
	mutex sync.RWMutex
}

func newNodeManager() *nodeManager {
	return &nodeManager{
		nodes: make(map[string]*util.NodeInfo),
	}
}

func (m *nodeManager) addNode(nodeID string, nodeInfo *util.NodeInfo) {
	if nodeInfo == nil || len(nodeInfo.Devices) == 0 {
		return
	}
	m.mutex.Lock()
	defer m.mutex.Unlock()
	_, ok := m.nodes[nodeID]
	if ok {
		if len(nodeInfo.Devices) > 0 {
			tmp := make([]util.DeviceInfo, 0, len(nodeInfo.Devices))
			devices := device.GetDevices()
			deviceType := ""
			for _, val := range devices {
				if strings.Contains(nodeInfo.Devices[0].Type, val.CommonWord()) {
					deviceType = val.CommonWord()
				}
			}
			for _, val := range m.nodes[nodeID].Devices {
				if !strings.Contains(val.Type, deviceType) {
					tmp = append(tmp, val)
				}
			}
			m.nodes[nodeID].Devices = tmp
			m.nodes[nodeID].Devices = append(m.nodes[nodeID].Devices, nodeInfo.Devices...)
		}
		m.nodes[nodeID].Node = nodeInfo.Node
	} else {
		m.nodes[nodeID] = nodeInfo
	}
}

func (m *nodeManager) rmNodeDevices(nodeID string, deviceVendor string) {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	nodeInfo := m.nodes[nodeID]
	if nodeInfo == nil {
		return
	}

	devices := make([]util.DeviceInfo, 0)
	for _, val := range nodeInfo.Devices {
		if val.DeviceVendor != deviceVendor {
			devices = append(devices, val)
		}
	}

	if len(devices) == 0 {
		delete(m.nodes, nodeID)
	} else {
		nodeInfo.Devices = devices
	}
	klog.InfoS("Removing device from node", "nodeName", nodeID, "deviceVendor", deviceVendor, "remainingDevices", devices)
}

func (m *nodeManager) RefreshDeviceLicense(nodeID, uuid string, t time.Time) bool {
	n, ok := m.nodes[nodeID]
	if !ok {
		return false
	}
	for idx := range n.Devices {
		if n.Devices[idx].ID == uuid {
			n.Devices[idx].LicenseExpire = t
			klog.InfoS("LicenseRefeshed for device", "node", nodeID, "uuid", uuid, "time", t)
			return true
		}
	}
	return false
}

func (m *nodeManager) GetNode(nodeID string) (*util.NodeInfo, error) {
	m.mutex.RLock()
	defer m.mutex.RUnlock()
	if n, ok := m.nodes[nodeID]; ok {
		return n, nil
	}
	return &util.NodeInfo{}, fmt.Errorf("node %v not found", nodeID)
}

func (m *nodeManager) ListNodes() (map[string]*util.NodeInfo, error) {
	m.mutex.RLock()
	defer m.mutex.RUnlock()
	return m.nodes, nil
}

func getDeviceUsage(usage *NodeUsage, uuid string) *util.DeviceUsage {
	for idx, val := range usage.Devices.DeviceLists {
		if val.Device.ID == uuid {
			return usage.Devices.DeviceLists[idx].Device
		}
	}
	return nil
}

func (m *nodeManager) IterateOverNodeDevices(nodes map[string]*NodeUsage, keys []string, uuid, cardtype string, l *license.LicenseManager) (time.Time, bool) {
	for _, idx := range keys {
		for devi, dev := range m.nodes[idx].Devices {
			usage := getDeviceUsage(nodes[idx], dev.ID)
			if usage.Used == 0 && usage.Validate && !usage.LicenseExpire.IsZero() {
				replace, t := l.Replace(usage.ID, usage.Type, uuid, cardtype)
				if replace {
					usage.LicenseExpire = time.Time{}
					m.nodes[idx].Devices[devi].LicenseExpire = time.Time{}
					return t, true
				}
			}
		}
	}
	return time.Time{}, false
}

func (m *nodeManager) SelectUnusedDevice(usage map[string]*NodeUsage, l *license.LicenseManager) {
	keys := []string{}
	for idx := range m.nodes {
		keys = append(keys, idx)
	}
	slices.Sort(keys)
	for _, idx := range keys {
		val := m.nodes[idx]
		nodeUsage := usage[idx]
		for devIdx, dev := range val.Devices {
			devusage := getDeviceUsage(nodeUsage, dev.ID)
			if devusage.Used > 0 && devusage.Validate && devusage.LicenseExpire.IsZero() {
				t, replace := m.IterateOverNodeDevices(usage, keys, devusage.ID, devusage.Type, l)
				if replace {
					devusage.LicenseExpire = t
					val.Devices[devIdx].LicenseExpire = t
				}
			}
		}
	}
}
