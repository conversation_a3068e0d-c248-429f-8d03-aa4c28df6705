/*
Copyright 2024 The HAMi Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package scheduler

import (
	"fmt"
	"testing"
	"time"

	"github.com/stretchr/testify/require"
	"gotest.tools/v3/assert"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"github.com/Project-HAMi/HAMi/pkg/device"
	"github.com/Project-HAMi/HAMi/pkg/license"
	"github.com/Project-HAMi/HAMi/pkg/scheduler/policy"
	"github.com/Project-HAMi/HAMi/pkg/util"
)

func Test_addNode_ListNodes(t *testing.T) {
	tests := []struct {
		name string
		args struct {
			nodeID   string
			nodeInfo util.NodeInfo
		}
		want map[string]*util.NodeInfo
		err  error
	}{
		{
			name: "node info is empty",
			args: struct {
				nodeID   string
				nodeInfo util.NodeInfo
			}{
				nodeID:   "node-01",
				nodeInfo: util.NodeInfo{},
			},
		},
		{
			name: "test vaild info",
			args: struct {
				nodeID   string
				nodeInfo util.NodeInfo
			}{
				nodeID: "node-01",
				nodeInfo: util.NodeInfo{
					ID:   "node-01",
					Node: &corev1.Node{},
					Devices: []util.DeviceInfo{
						{
							ID: "node-01",
						},
					},
				},
			},
			want: map[string]*util.NodeInfo{
				"node-01": {
					ID:   "test123",
					Node: &corev1.Node{},
					Devices: []util.DeviceInfo{
						{
							ID: "node-01",
						},
					},
				},
			},
			err: nil,
		},
		{
			name: "test the different node id",
			args: struct {
				nodeID   string
				nodeInfo util.NodeInfo
			}{
				nodeID: "node-02",
				nodeInfo: util.NodeInfo{
					ID:   "node-02",
					Node: &corev1.Node{},
					Devices: []util.DeviceInfo{
						{
							ID:      "node-02",
							Count:   int32(1),
							Devcore: int32(1),
							Devmem:  int32(2000),
						},
					},
				},
			},
			want: map[string]*util.NodeInfo{
				"node-01": {
					ID:   "test123",
					Node: &corev1.Node{},
					Devices: []util.DeviceInfo{
						{
							ID:      "GPU-0",
							Count:   int32(1),
							Devcore: int32(1),
							Devmem:  int32(2000),
						},
					},
				},
				"node-02": {
					ID:   "node-02",
					Node: &corev1.Node{},
					Devices: []util.DeviceInfo{
						{
							ID:      "node-02",
							Count:   int32(1),
							Devcore: int32(1),
							Devmem:  int32(2000),
						},
					},
				},
			},
			err: nil,
		},
	}
	device.InitDefaultDevices()
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			m := nodeManager{
				nodes: map[string]*util.NodeInfo{
					"node-01": {
						ID:   "test123",
						Node: &corev1.Node{},
						Devices: []util.DeviceInfo{
							{
								ID:      "GPU-0",
								Count:   int32(1),
								Devcore: int32(1),
								Devmem:  int32(2000),
							},
						},
					},
				},
			}
			m.addNode(test.args.nodeID, &test.args.nodeInfo)
			if len(test.want) != 0 {
				result, err := m.ListNodes()
				if err == nil {
					assert.DeepEqual(t, test.want, result)
				}
			}
		})
	}
}

func Test_GetNode(t *testing.T) {
	tests := []struct {
		name string
		args string
		want *util.NodeInfo
		err  error
	}{
		{
			name: "node not found",
			args: "node-1111",
			want: &util.NodeInfo{},
			err:  fmt.Errorf("node %v not found", "node-111"),
		},
		{
			name: "test vaild info",
			args: "node-04",
			want: &util.NodeInfo{
				ID:   "node-04",
				Node: &corev1.Node{},
				Devices: []util.DeviceInfo{
					{
						ID:      "GPU-0",
						Count:   int32(1),
						Devcore: int32(1),
						Devmem:  int32(2000),
					},
				},
			},
			err: nil,
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			m := nodeManager{
				nodes: map[string]*util.NodeInfo{
					"node-04": {
						ID:   "node-04",
						Node: &corev1.Node{},
						Devices: []util.DeviceInfo{
							{
								ID:      "GPU-0",
								Count:   int32(1),
								Devcore: int32(1),
								Devmem:  int32(2000),
							},
						},
					},
				},
			}
			result, err := m.GetNode(test.args)
			if err != nil {
				assert.DeepEqual(t, test.want, result)
			}
		})
	}
}

func Test_rmNodeDevices(t *testing.T) {
	tests := []struct {
		name string
		args struct {
			nodeID       string
			deviceVendor string
		}
	}{
		{
			name: "no device",
			args: struct {
				nodeID       string
				deviceVendor string
			}{
				nodeID: "node-06",
			},
		},
		{
			name: "exist device info",
			args: struct {
				nodeID       string
				deviceVendor string
			}{
				nodeID:       "node-05",
				deviceVendor: "NVIDIA",
			},
		},
		{
			name: "the different devicevendor",
			args: struct {
				nodeID       string
				deviceVendor string
			}{
				nodeID:       "node-07",
				deviceVendor: "NVIDIA",
			},
		},
		{
			name: "the same of device id no less than one",
			args: struct {
				nodeID       string
				deviceVendor string
			}{
				nodeID:       "node-08",
				deviceVendor: "NVIDIA",
			},
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			m := nodeManager{
				nodes: map[string]*util.NodeInfo{
					"node-05": {
						ID:   "node-05",
						Node: &corev1.Node{},
						Devices: []util.DeviceInfo{
							{
								ID:           "GPU-0",
								Count:        int32(1),
								Devcore:      int32(1),
								Devmem:       int32(2000),
								DeviceVendor: "NVIDIA",
							},
						},
					},
					"node-06": {
						ID:      "node-06",
						Node:    &corev1.Node{},
						Devices: []util.DeviceInfo{},
					},
					"node-07": {
						ID:   "node-17",
						Node: &corev1.Node{},
						Devices: []util.DeviceInfo{
							{
								ID:           "GPU-0",
								Count:        int32(1),
								Devcore:      int32(1),
								Devmem:       int32(2000),
								DeviceVendor: "test",
							},
						},
					},
					"node-08": {
						ID:   "node-08",
						Node: &corev1.Node{},
						Devices: []util.DeviceInfo{
							{
								ID:           "GPU-0",
								Count:        int32(1),
								Devcore:      int32(1),
								Devmem:       int32(2000),
								DeviceVendor: "NVIDIA",
							},
							{
								ID:           "GPU-0",
								Count:        int32(1),
								Devcore:      int32(1),
								Devmem:       int32(2000),
								DeviceVendor: "NVIDIA",
							},
						},
					},
				},
			}
			m.rmNodeDevices(test.args.nodeID, test.args.deviceVendor)
		})
	}
}

func TestIterateOverNodeDevices(t *testing.T) {
	tests := []struct {
		name          string
		nodes         map[string]*NodeUsage
		licenseUsage  []*license.LicenseUsage
		keys          []string
		uuid          string
		cardtype      string
		expectedTime  time.Time
		expectedFound bool
	}{
		{
			name: "successful replacement",
			nodes: map[string]*NodeUsage{
				"node1": {
					Devices: policy.DeviceUsageList{
						DeviceLists: []*policy.DeviceListsScore{
							{
								Device: &util.DeviceUsage{
									ID:            "GPU-123",
									Type:          "NVIDIA A10",
									Used:          0,
									Validate:      true,
									LicenseExpire: time.Date(2024, 12, 31, 23, 59, 59, 0, time.UTC),
								},
							},
						},
					},
				},
			},
			keys:     []string{"node1"},
			uuid:     "GPU-456",
			cardtype: "NVIDIA A10",
			// licenseUsage: []*license.LicenseUsage{
			// 	{
			// 		LicenseT: license.License{
			// 			DeviceModels: &license.Model{
			// 				Count:       2,
			// 				DeviceTypes: []string{"NVIDIA A10"},
			// 			},
			// 			ExpireTime: time.UnixMilli(1111),
			// 		},
			// 		Used: 2,
			// 	},
			// },
			expectedTime:  time.UnixMilli(1111),
			expectedFound: true,
		},
		{
			name: "device in use",
			nodes: map[string]*NodeUsage{
				"node1": {
					Devices: policy.DeviceUsageList{
						DeviceLists: []*policy.DeviceListsScore{
							{
								Device: &util.DeviceUsage{
									ID:            "GPU-123",
									Type:          "NVIDIA",
									Used:          1, // Device is in use
									Validate:      true,
									LicenseExpire: time.Date(2024, 12, 31, 23, 59, 59, 0, time.UTC),
								},
							},
						},
					},
				},
			},
			// licenseUsage: []*license.LicenseUsage{
			// 	{
			// 		LicenseT: license.License{
			// 			DeviceModels: &license.Model{
			// 				Count:       2,
			// 				DeviceTypes: []string{"NVIDIA A10"},
			// 			},
			// 			ExpireTime: time.UnixMilli(1111),
			// 		},
			// 		Used: 2,
			// 	},
			// },
			keys:          []string{"node1"},
			uuid:          "GPU-456",
			cardtype:      "NVIDIA A10",
			expectedTime:  time.Time{},
			expectedFound: false,
		},
		{
			name: "license expired",
			nodes: map[string]*NodeUsage{
				"node1": {
					Devices: policy.DeviceUsageList{
						DeviceLists: []*policy.DeviceListsScore{
							{
								Device: &util.DeviceUsage{
									ID:            "GPU-123",
									Type:          "NVIDIA",
									Used:          0,
									Validate:      true,
									LicenseExpire: time.Time{}, // License expired
								},
							},
						},
					},
				},
			},
			// licenseUsage: []*license.LicenseUsage{
			// 	{
			// 		LicenseT: license.License{
			// 			DeviceModels: &license.Model{
			// 				Count:       2,
			// 				DeviceTypes: []string{"NVIDIA A10"},
			// 			},
			// 			ExpireTime: time.UnixMilli(1111),
			// 		},
			// 		Used: 2,
			// 	},
			// },
			keys:          []string{"node1"},
			uuid:          "GPU-456",
			cardtype:      "NVIDIA A10",
			expectedTime:  time.Time{},
			expectedFound: false,
		},
		{
			name: "no matching devices",
			nodes: map[string]*NodeUsage{
				"node1": {
					Devices: policy.DeviceUsageList{
						DeviceLists: []*policy.DeviceListsScore{
							{
								Device: &util.DeviceUsage{
									ID:            "GPU-789",
									Type:          "Cambricon",
									Used:          0,
									Validate:      true,
									LicenseExpire: time.Date(2024, 12, 31, 23, 59, 59, 0, time.UTC),
								},
							},
						},
					},
				},
			},
			keys:     []string{"node1"},
			uuid:     "GPU-456",
			cardtype: "NVIDIA A40",
			// licenseUsage: []*license.LicenseUsage{
			// 	{
			// 		LicenseT: license.License{
			// 			DeviceModels: &license.Model{
			// 				Count:       2,
			// 				DeviceTypes: []string{"NVIDIA A10"},
			// 			},
			// 			ExpireTime: time.UnixMilli(1111),
			// 		},
			// 		Used: 2,
			// 	},
			// },
			expectedTime:  time.Time{},
			expectedFound: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create scheduler with mock nodes
			ts := NewScheduler()
			ts.licenses = license.LicenseManager{}
			ts.licenses.Licenses = tt.licenseUsage

			// Populate scheduler nodes
			for nodeKey, nodeUsage := range tt.nodes {
				nodeInfo := &util.NodeInfo{
					ID: nodeKey,
					Node: &corev1.Node{
						ObjectMeta: metav1.ObjectMeta{
							Name: nodeKey,
						},
					},
					Devices: []util.DeviceInfo{},
				}

				for _, deviceList := range nodeUsage.Devices.DeviceLists {
					nodeInfo.Devices = append(nodeInfo.Devices, util.DeviceInfo{
						ID:            deviceList.Device.ID,
						Type:          deviceList.Device.Type,
						Validate:      deviceList.Device.Validate,
						LicenseExpire: deviceList.Device.LicenseExpire,
					})
				}

				ts.addNode(nodeKey, nodeInfo)
			}

			resultTime, resultFound := ts.IterateOverNodeDevices(tt.nodes, tt.keys, tt.uuid, tt.cardtype, &ts.licenses)

			assert.Equal(t, tt.expectedFound, resultFound)
			if tt.expectedFound {
				assert.Equal(t, tt.expectedTime, resultTime)
				assert.Equal(t, time.Time{}, tt.nodes[tt.keys[0]].Devices.DeviceLists[0].Device.LicenseExpire)
				assert.Equal(t, time.Time{}, ts.nodes[tt.keys[0]].Devices[0].LicenseExpire)
			}
		})
	}
}

func TestSelectUnusedDevice(t *testing.T) {
	tests := []struct {
		name            string
		nodes           map[string]*util.NodeInfo
		usage           map[string]*NodeUsage
		licenseUsage    []*license.LicenseUsage
		expectedChanges map[string]map[string]time.Time // nodeID -> deviceID -> expected expire time
	}{
		{
			name: "successful device selection and replacement",
			nodes: map[string]*util.NodeInfo{
				"node1": {
					ID: "node1",
					Devices: []util.DeviceInfo{
						{
							ID:            "GPU-123",
							Type:          "NVIDIA A10",
							Validate:      true,
							LicenseExpire: time.Time{}, // Expired license
						},
						{
							ID:            "GPU-456",
							Type:          "NVIDIA A10",
							Validate:      true,
							LicenseExpire: time.Date(2024, 12, 31, 23, 59, 59, 0, time.UTC),
						},
					},
				},
			},
			// licenseUsage: []*license.LicenseUsage{
			// 	{
			// 		LicenseT: license.License{
			// 			DeviceModels: &license.Model{
			// 				Count:       2,
			// 				DeviceTypes: []string{"NVIDIA A10"},
			// 			},
			// 			ExpireTime: time.UnixMilli(1111),
			// 		},
			// 		Used: 2,
			// 	},
			// },
			usage: map[string]*NodeUsage{
				"node1": {
					Devices: policy.DeviceUsageList{
						DeviceLists: []*policy.DeviceListsScore{
							{
								Device: &util.DeviceUsage{
									ID:            "GPU-123",
									Type:          "NVIDIA A10",
									Used:          1, // Device is in use
									Validate:      true,
									LicenseExpire: time.Time{}, // Expired license
								},
							},
							{
								Device: &util.DeviceUsage{
									ID:            "GPU-456",
									Type:          "NVIDIA A10",
									Used:          0, // Device is free
									Validate:      true,
									LicenseExpire: time.Date(2024, 12, 31, 23, 59, 59, 0, time.UTC),
								},
							},
						},
					},
				},
			},
			expectedChanges: map[string]map[string]time.Time{
				"node1": {
					"GPU-123": time.UnixMilli(1111),
					"GPU-456": time.Time{},
				},
			},
		},
		{
			name: "no devices need replacement",
			nodes: map[string]*util.NodeInfo{
				"node1": {
					ID: "node1",
					Devices: []util.DeviceInfo{
						{
							ID:            "GPU-123",
							Type:          "NVIDIA A10",
							Validate:      true,
							LicenseExpire: time.Date(2024, 12, 31, 23, 59, 59, 0, time.UTC),
						},
					},
				},
			},
			// licenseUsage: []*license.LicenseUsage{
			// 	{
			// 		LicenseT: license.License{
			// 			DeviceModels: &license.Model{
			// 				Count:       2,
			// 				DeviceTypes: []string{"NVIDIA A10"},
			// 			},
			// 			ExpireTime: time.UnixMilli(1111),
			// 		},
			// 		Used: 2,
			// 	},
			// },
			usage: map[string]*NodeUsage{
				"node1": {
					Devices: policy.DeviceUsageList{
						DeviceLists: []*policy.DeviceListsScore{
							{
								Device: &util.DeviceUsage{
									ID:            "GPU-123",
									Type:          "NVIDIA A10",
									Used:          0, // Device is free
									Validate:      true,
									LicenseExpire: time.Date(2024, 12, 31, 23, 59, 59, 0, time.UTC),
								},
							},
						},
					},
				},
			},
			expectedChanges: map[string]map[string]time.Time{},
		},
		{
			name: "successful device selection and replacement across multiple nodes",
			nodes: map[string]*util.NodeInfo{
				"node1": {
					ID: "node1",
					Devices: []util.DeviceInfo{
						{
							ID:            "GPU-123",
							Type:          "NVIDIA A10",
							Validate:      true,
							LicenseExpire: time.Time{}, // Expired license
						},
					},
				},
				"node2": {
					ID: "node2",
					Devices: []util.DeviceInfo{
						{
							ID:            "GPU-456",
							Type:          "NVIDIA A10",
							Validate:      true,
							LicenseExpire: time.Date(2024, 12, 31, 23, 59, 59, 0, time.UTC),
						},
					},
				},
			},
			// licenseUsage: []*license.LicenseUsage{
			// 	{
			// 		LicenseT: license.License{
			// 			DeviceModels: &license.Model{
			// 				Count:       2,
			// 				DeviceTypes: []string{"NVIDIA A10"},
			// 			},
			// 			ExpireTime: time.UnixMilli(1111),
			// 		},
			// 		Used: 2,
			// 	},
			// },
			usage: map[string]*NodeUsage{
				"node1": {
					Devices: policy.DeviceUsageList{
						DeviceLists: []*policy.DeviceListsScore{
							{
								Device: &util.DeviceUsage{
									ID:            "GPU-123",
									Type:          "NVIDIA A10",
									Used:          1, // Device is in use
									Validate:      true,
									LicenseExpire: time.Time{}, // Expired license
								},
							},
						},
					},
				},
				"node2": {
					Devices: policy.DeviceUsageList{
						DeviceLists: []*policy.DeviceListsScore{
							{
								Device: &util.DeviceUsage{
									ID:            "GPU-456",
									Type:          "NVIDIA A10",
									Used:          0, // Device is free
									Validate:      true,
									LicenseExpire: time.Date(2024, 12, 31, 23, 59, 59, 0, time.UTC),
								},
							},
						},
					},
				},
			},
			expectedChanges: map[string]map[string]time.Time{
				"node1": {
					"GPU-123": time.UnixMilli(1111),
				},
				"node2": {
					"GPU-456": time.Time{},
				},
			},
		},
		{
			name: "multiple nodes sorted by name",
			nodes: map[string]*util.NodeInfo{
				"node2": {
					ID: "node2",
					Devices: []util.DeviceInfo{
						{
							ID:            "GPU-456",
							Type:          "NVIDIA A10",
							Validate:      true,
							LicenseExpire: time.Time{},
						},
					},
				},
				"node1": {
					ID: "node1",
					Devices: []util.DeviceInfo{
						{
							ID:            "GPU-123",
							Type:          "NVIDIA A10",
							Validate:      true,
							LicenseExpire: time.Time{},
						},
					},
				},
			},
			usage: map[string]*NodeUsage{
				"node1": {
					Devices: policy.DeviceUsageList{
						DeviceLists: []*policy.DeviceListsScore{
							{
								Device: &util.DeviceUsage{
									ID:            "GPU-123",
									Type:          "NVIDIA A10",
									Used:          1,
									Validate:      true,
									LicenseExpire: time.Time{},
								},
							},
						},
					},
				},
				"node2": {
					Devices: policy.DeviceUsageList{
						DeviceLists: []*policy.DeviceListsScore{
							{
								Device: &util.DeviceUsage{
									ID:            "GPU-456",
									Type:          "NVIDIA A10",
									Used:          1,
									Validate:      true,
									LicenseExpire: time.Time{},
								},
							},
						},
					},
				},
			},
			expectedChanges: map[string]map[string]time.Time{},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ts := NewScheduler()
			ts.licenses = license.InitLicenseManager()
			ts.licenses.Licenses = tt.licenseUsage
			// Copy nodes to scheduler
			for nodeID, nodeInfo := range tt.nodes {
				ts.addNode(nodeID, nodeInfo)
			}

			ts.SelectUnusedDevice(tt.usage, &ts.licenses)

			// Verify expected changes
			for nodeID, deviceChanges := range tt.expectedChanges {
				nodeInfo, exists := ts.nodes[nodeID]
				require.True(t, exists, "Node %s should exist", nodeID)

				for deviceID, expectedExpire := range deviceChanges {
					// Check node devices
					for _, device := range nodeInfo.Devices {
						if device.ID == deviceID {
							assert.Equal(t, expectedExpire, device.LicenseExpire,
								"Device %s in node %s should have expected expire time", deviceID, nodeID)
							break
						}
					}

					// Check usage devices
					nodeUsage, exists := tt.usage[nodeID]
					require.True(t, exists, "Node usage for %s should exist", nodeID)

					for _, deviceList := range nodeUsage.Devices.DeviceLists {
						if deviceList.Device.ID == deviceID {
							assert.Equal(t, expectedExpire, deviceList.Device.LicenseExpire,
								"Device %s in usage for node %s should have expected expire time", deviceID, nodeID)
							break
						}
					}
				}
			}
		})
	}
}
